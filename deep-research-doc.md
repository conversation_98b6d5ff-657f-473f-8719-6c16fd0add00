Building a Scalable High-Throughput DuckDB + Delta + Arrow API
Architecture Overview

To serve 100+ queries per second on S3-hosted data, the system should be a stateless, scalable microservice that embeds DuckDB for query execution. Each API instance runs an embedded DuckDB engine with the Delta Lake extension loaded, so it can directly query Delta table files on S3 without pre-loading data. The service exposes a simple HTTP interface (e.g. REST endpoints) that accept filter parameters (date ranges, bounding boxes, asset IDs, etc.) instead of arbitrary SQL. The server translates these parameters into an internal DuckDB SQL query and streams the results back as an Arrow IPC stream over HTTP. Because requests are short-lived and carry no session state, any instance can handle any request, allowing easy horizontal scaling behind a load balancer. This stateless design also means instances can be added or removed (via autoscaling) without disrupting clients, which is essential for bursty traffic.

Key components of the design:

Embedded DuckDB + Delta – Each server process runs DuckDB with the Delta extension to read the Delta table on S3. DuckDB handles predicate pushdown, file pruning, and caching internally for efficiency
duckdb.org
duckdb.org
.

HTTP + Arrow IPC Streaming – Query results are returned as a stream of Arrow record batches (binary IPC format) in the HTTP response (with content-type application/vnd.apache.arrow.stream). This avoids JSON serialization overhead and yields a compact, zero-copy transfer to clients
github.com
.

Parameterized API (No client SQL) – Rather than accepting raw SQL from clients, the API defines fixed query patterns (e.g. filter by date/bbox). Clients pass parameters, and the server safely constructs the SQL. This limits complexity and prevents SQL injection, since only supported queries can be run.

Stateless, Short Requests – Each request stands alone (includes all parameters needed) and opens no persistent connection. The server executes the DuckDB query and streams the result back, then the connection closes. There are no long-held server sessions or open cursors, which keeps resource usage predictable and makes load balancing trivial.

This architecture ensures simplicity: clients only deal with HTTP requests and Arrow-formatted responses, and the server can fully leverage DuckDB’s analytics performance on object-store data while remaining stateless and scalable.

Query API Design (Parameters & Arrow Streaming)

The API should be designed around simple HTTP endpoints that accept query parameters and return an Arrow stream. For example, one might define a GET endpoint like /query (or /data) where clients provide filters such as start_date, end_date, bbox, or asset_key as query params or JSON. The service then maps these to a parameterized SQL query against the Delta table. For instance, if the Delta table has columns timestamp, latitude/longitude or similar, the service could generate a query:

SELECT * 
FROM delta_scan('s3://bucket/table') 
WHERE date BETWEEN $start AND $end 
  AND lat BETWEEN $minLat AND $maxLat 
  AND lon BETWEEN $minLon AND $maxLon 
  AND asset = $assetKey;


DuckDB’s delta_scan function (or an attached table – see below) will apply these filters with predicate pushdown, so only relevant Parquet files are read
duckdb.org
duckdb.org
. The client never sends raw SQL – they only specify high-level filters. This keeps the API surface narrow and safe, and offloads query composition to the server.

Arrow IPC over HTTP: The service returns results in the Apache Arrow IPC streaming format. The HTTP response uses Content-Type: application/vnd.apache.arrow.stream, and the body is a sequence of Arrow record batches (with an initial schema message) that represent the result set. This binary format is very efficient: it includes the schema and column data in a compact form, allowing zero-copy reads in many languages
stackoverflow.com
. Clients can use Arrow libraries (for example, Arrow JS in a browser or PyArrow in Python) to directly consume the response. For instance, in JavaScript one can simply do:

const table = await tableFromIPC(fetch(url));


to parse the streamed Arrow data into an Arrow Table. This approach avoids JSON or CSV encoding, greatly reducing serialization overhead and latency.

Notably, we do not use Arrow Flight on the client side. Arrow Flight is a gRPC-based protocol for Arrow data, but it requires specialized clients and a stateful server. Here, it’s unnecessary – we achieve streaming of Arrow data with plain HTTP. As one Arrow developer commented, “You don't have to use Flight to send Arrow data. You could serialize the Arrow data using the Arrow stream writer, send it over HTTP ... and load it from the browser using the Arrow JavaScript libraries.”
stackoverflow.com
. In other words, the Arrow IPC stream works fine over standard HTTP or WebSockets without the complexity of Flight. On the server side, implementing an Arrow Flight server is also not needed for our use case – Flight is designed for long-lived connections and fancy RPC (it’s built on gRPC and protobuf
arrow.apache.org
), whereas our service can keep things simpler by just responding to each HTTP request with an Arrow stream. This avoids additional layers and makes the system more interoperable (any HTTP client can download the Arrow data).

Minimizing per-request overhead: Each request goes through a lightweight code path: parse the input parameters, formulate the SQL (or use a prepared query template), execute it in DuckDB, and stream out the result. We avoid expensive setup/teardown on each query. For example, the DuckDB database and S3 connections should remain open between requests within an instance, so we don’t pay the cost of initializing DuckDB or negotiating with S3 on every query. The Arrow IPC format itself is compact and binary, so response encoding is very fast (DuckDB can produce Arrow data directly in memory). By constraining the API to a known set of filter parameters, we can even prepare parameterized statements in DuckDB at startup and just bind values for each query, which skips SQL parsing/planning on each call. Overall, the API shape is kept simple and declarative, which helps keep each request’s overhead low.

Efficient Query Execution with DuckDB and Delta on S3

Using DuckDB with the Delta Lake table format on S3 gives us a lot of built-in performance optimization. The DuckDB delta extension allows DuckDB to treat a Delta Lake repository as a table, with support for predicate pushdown, column projection, metadata caching, and file skipping. The engine will only read the portions of data necessary to satisfy each query’s filters:

Metadata-based Pruning: DuckDB will read the Delta table’s transaction log and statistics to determine which Parquet files contain rows matching the filter. Files outside the date range or spatial bounds, for example, will be skipped entirely. This file-level skipping occurs automatically based on Delta’s min/max statistics and partition info
duckdb.org
. The effect is dramatic – as shown in a DuckDB blog, a selective filter (e.g. WHERE id < 100) caused DuckDB to scan only 1 out of 2000 files, because it applied the filter as a file filter to prune the file list before reading
duckdb.org
duckdb.org
. This predicate pushdown and file elimination means each query only incurs I/O proportional to the size of relevant data, not the entire dataset.

Partition Pushdown: If the Delta table is partitioned (e.g. by date or region), DuckDB will also push those predicates into the scan. Only the partitions matching the filters will be opened. Partition information can even be used to optimize aggregations (DuckDB v1.2+ supports partition-aware processing in the Delta extension).

Column Projection: DuckDB only reads the columns needed. Our API might default to returning all columns, but if not, DuckDB will apply projection pushdown so that unnecessary columns (Parquet fields) aren’t read from S3
duckdb.org
. This reduces bandwidth and CPU usage.

Caching and ATTACH: To maximize throughput, we want to amortize one-time costs like reading table metadata. DuckDB’s Delta extension introduced a feature to attach a Delta table as a DuckDB table, which enables metadata caching across queries
duckdb.org
duckdb.org
. In practice, when the service starts, it can execute:

ATTACH 's3://my-bucket/path/to/delta_table' AS mydata (TYPE delta, PIN_SNAPSHOT);


This mounts the Delta table as mydata in DuckDB’s catalog. The first time, DuckDB will load the Delta log and file statistics; with PIN_SNAPSHOT, it will hold that metadata in memory for reuse
duckdb.org
. Subsequent queries can simply SELECT * FROM mydata WHERE ... without re-reading the metadata each time. According to DuckDB, “using ATTACH will allow DuckDB to cache/reuse certain parts of the Delta metadata, which can improve query performance.”
duckdb.org
. In benchmarks, keeping metadata cached improved query runtime by ~1.13×, and pinning the snapshot (fully caching metadata between queries) gave up to 1.47× speedup for repeated queries
duckdb.org
. In short, metadata caching eliminates a lot of overhead in scanning large Delta tables, especially when many queries hit the same table repeatedly.

Trade-off: With PIN_SNAPSHOT, the service is querying a fixed snapshot of the data – new data added to the Delta table won’t be seen until the table is re-attached or the DuckDB instance is restarted. If the dataset is updated frequently, we might choose not to pin, or periodically refresh the attachment (e.g. detach and re-attach nightly or when no traffic). But for mostly static or append-only data where real-time freshness isn’t critical, pinning is a big win for speed.

S3 I/O and HTTPFS: DuckDB’s S3 access (via the httpfs extension) is efficient and supports throughput optimizations. It will use HTTP range requests to only fetch needed parts of Parquet files (thanks to pushdown). It also can use multiple threads to read multiple files in parallel, and it reuses TCP connections to S3. We should ensure that DuckDB is configured with a sufficient number of worker threads so that it can fetch data in parallel and use multiple CPU cores for scan and decompress. Typically, DuckDB will default to using all available cores for a query, which is fine, but in a high-QPS scenario, we may want to tune the threading: for example, allow each query to use up to N threads and leave some headroom for concurrent queries.

Additionally, since each server instance is long-lived, OS-level caching will come into play: recently read file segments from S3 may be cached in memory by the kernel. If the same small subset of data is queried repeatedly (a “hot” slice of the dataset), those Parquet pages might already reside in memory after the first query, making subsequent queries much faster. DuckDB itself doesn’t yet have a user-controlled disk cache for remote files (at least not as of early 2025), but the combination of persistent processes and OS cache provides a similar effect for hot data. In an issue discussion, users considered caching hot data files in memory to avoid repeated cloud reads
github.com
 – with our architecture, keeping the process warm and using attach caching achieves much of this automatically.

TL;DR: DuckDB’s integration with Delta Lake ensures that even though our data is on object storage, each query will only scan what’s necessary (thanks to predicate pushdown and file skipping), and by caching metadata in memory, we avoid repeatedly parsing file lists or statistics. This allows the service to handle many queries on a large dataset without reading terabytes for each small request.

Concurrency and Throughput Considerations

Achieving 100+ queries per second reliably means handling a high degree of concurrency. DuckDB is an in-process database optimized for heavy analytical queries rather than thousands of tiny concurrent transactions
duckdb.org
. We need to design around this limitation:

Multiple Connections / Threads: We will run DuckDB in a multi-threaded mode, using multiple connections or contexts so that queries can execute in parallel. DuckDB does support concurrent reads in a single process – you can open multiple connections to the same database and perform queries simultaneously (writes use MVCC for concurrency, but our use-case is mostly read-only)
duckdb.org
. In practice, the service can maintain a pool of DuckDB connections. Each incoming request is handled by a worker thread (or async task) which uses a connection from the pool to execute the query. This avoids any global lock that forces sequential query execution. Note that a single DuckDB connection is not thread-safe to use from multiple threads at once, but multiple connections can operate in parallel. Also, DuckDB can internally use multiple threads per query for scan/compute, but we may limit that to avoid contention when many queries run at once.

Single vs. Shared Database: We can open the DuckDB database file (or an in-memory DB) once per process and share it among connections. For example, using a :memory: database with the Delta table attached means all connections refer to the same in-memory state (same attached table and caches). This is ideal because it means the metadata cache and any data cached by the OS is shared in the process. If we instead opened separate DuckDB instances per query, we’d lose the caching benefits and incur a lot of overhead. So, the service should initialize one DuckDB database on startup (loading needed extensions, attaching tables, etc.), and then handle queries on that DB concurrently. DuckDB’s docs note that keeping queries on the same connection/process allows caching of data, query plans, and catalog state so that “subsequent queries on the same connection are faster.”
duckdb.org
 We leverage this by not terminating the process or connection between queries.

Throughput vs. Query Cost: Because DuckDB is optimized for bulk operations more than many tiny operations
duckdb.org
, we must be careful with very high QPS. If each query is extremely light (e.g., a point lookup), DuckDB’s engine might spend non-trivial time just starting up the query (planning, etc.). We mitigate this with prepared statements and caching as discussed. If queries involve scanning even moderately large chunks, the engine will utilize vectorized execution and multiple threads, which is good for single-query latency but means CPU usage will spike per query. At 100 QPS, those spikes overlap. Thus, horizontal scaling is essential (discussed in the next section). We shouldn’t expect one process to handle 100 QPS unless each query is trivial. Instead, we might run, say, 10 instances each handling ~10 QPS, or 20 instances at ~5 QPS, depending on query complexity. This provides headroom and prevents overload.

Avoiding Bottlenecks: It’s important to avoid any single bottleneck in the request path. The design avoids an external database in the middle (DuckDB is the query engine on the data). Also, by streaming results, we don’t have to buffer large responses fully in memory. One thing to watch is network I/O to S3: if each query hits S3 for data, 100 QPS could potentially overwhelm either the S3 bucket I/O limits or the network bandwidth of the instances. In practice, thanks to pruning, not all queries will hit S3 heavily (many might be served from cache or only read small files). We can also consider enabling S3 transfer acceleration or using multiple S3 read threads if needed. The burstiness factor means we should handle short spikes – e.g. 200 queries in one second – without crashing. Queuing incoming requests briefly when overloaded can help smooth bursts.

Backpressure and Limits: To maintain stability, the service can impose a cap on concurrent queries per instance. For example, if an instance has 8 CPU cores, it might handle ~8 queries at a time (since each query could use one core). If more requests come in, they could be queued very briefly or routed to another instance by the load balancer. This prevents thrashing where too many queries run at once and all slow down or OOM. Essentially, avoid oversubscribing a single DuckDB process. It’s better to queue or scale out than to run 100 concurrent heavy queries on one process.

In summary, we’ll use concurrent DuckDB connections in each server and scale out the number of servers to ensure the aggregate throughput is high. DuckDB can do many things in parallel, but the workload needs to be balanced across multiple processes to truly handle 100+ QPS reliably. The concurrency strategy is to keep each instance busy but not overwhelmed, and leverage multiple instances for scaling.

Autoscaling and Burst Tolerance

To handle variable load and bursts of traffic, deploying the service on Kubernetes with autoscaling is a good approach. Both Horizontal Pod Autoscaler (HPA) and Kubernetes Event-Driven Autoscaling (KEDA) can be used to dynamically adjust capacity:

Horizontal Pod Autoscaler (HPA): We can configure HPA to monitor CPU utilization or custom metrics and add/remove pods accordingly. For instance, if each query tends to consume ~X CPU, we set a target CPU% such that at ~N concurrent queries the CPU hits the threshold, triggering a scale-out. HPA is a reactive mechanism (checking metrics every N seconds) – it may take a minute or two to scale from 1 pod to 10 pods under sudden load. This is generally acceptable for many cases, but extreme bursts could overflow the pod before HPA catches up. HPA can also use custom metrics (e.g. number of requests per second, or queue length) if integrated with a metrics system. For example, one can publish a Prometheus metric for current QPS or request backlog, and use that to scale pods
medium.com
medium.com
.

KEDA (Kubernetes Event-Driven Autoscaling): KEDA extends autoscaling with the ability to scale on external event metrics and to scale down to zero when idle. If our workload has long periods of no traffic and occasional bursts, scale-to-zero is very useful to save cost. KEDA has an HTTP add-on that can act as an event source for HTTP request rates. With KEDA’s HTTP scaler, a small interceptor/proxy sits in front of the service, counts incoming HTTP requests, and signals KEDA to spawn pods based on demand. The KEDA HTTP add-on can even queue requests briefly so that pods have time to spin up, ensuring the burst of requests all get handled once scaling catches up
github.com
github.com
. According to the KEDA project, “The KEDA HTTP Add-on allows Kubernetes users to automatically scale their HTTP servers up and down (including to/from zero) based on incoming HTTP traffic.”
github.com
. This means if no one is querying the API, we could run 0 pods, and when a burst of 100 QPS hits, KEDA will detect the spike and quickly launch enough pods (e.g. 10 pods) to handle it, then scale down afterwards. This kind of rapid, event-driven scaling is more burst-tolerant than HPA on CPU alone.

Burst buffer: If using KEDA’s HTTP interceptor or a similar queue (like a RabbitMQ or Kafka in front of a consumer), incoming requests can be temporarily buffered. However, since this is an HTTP API meant to respond in real-time, introducing a queue can add latency. A lightweight approach is to use the Ingress or load balancer to handle some buffering. Many ingress controllers can queue a few requests when targets are busy. We want to avoid dropping queries during a burst, so having a small queue (even in-process, e.g. using async workers) for overflow can help. The system should respond within seconds, so we can’t queue too much, but a few hundred milliseconds of buffering while a new pod initializes could bridge the gap.

Autoscaling config: We would set resource requests/limits on the pods (CPU & memory) and let HPA/KEDA add pods when utilization is high. For example, if one pod can comfortably do ~50 QPS at 70% CPU, and we expect bursts of 200 QPS, we might allow scaling up to 4-5 pods. In testing, we’d measure the 99th percentile latency and tune scaling policies to keep latency low even at peak load (scaling out before latency degrades).

Kubernetes HPA vs KEDA: They are not mutually exclusive – KEDA can feed custom metrics to HPA. In practice, KEDA’s HTTP add-on is specialized for request-driven scaling, whereas HPA by default might only see CPU. Given our scenario of unpredictable bursty queries, the event-driven approach (KEDA HTTP) is attractive. It even supports scale-to-zero, so if the API might sit idle for hours, we don’t waste a pod doing nothing. (When a new request comes in, KEDA’s interceptor will initiate scaling to 1 pod and serve the request once ready – cold start latency might be a couple of seconds, which may be acceptable for certain use cases.)

Startup and Warm-up: It’s important that when a new pod starts (either from HPA or KEDA), it quickly loads the DuckDB extension and attaches the Delta table. We can optimize the startup by baking the DuckDB extensions into the container image (so it doesn’t download them at runtime). The ATTACH could be run at startup so that the first actual query doesn’t pay that cost. This ensures new pods are ready to serve in minimal time. We should also consider that a brand new pod won’t have the metadata cache or OS cache that a long-running pod has – so its first few queries may be slower (cold start effect on performance). To mitigate this in a scale-up, one strategy is to send a “warming query” (like a lightweight query) when a pod comes up, or simply accept that the first query on a new pod might take longer. Since autoscaling events usually happen under load, other pods can carry the load briefly.

In essence, autoscaling is crucial for both throughput and cost-efficiency. We will use it to keep the service responsive under 100+ QPS bursts by rapidly scaling out, and then scale back in when load subsides. With the stateless design, scaling out just means launching more identical pods that each have their own DuckDB instance. There is no shared state that could become a bottleneck – S3 is the shared data source, which is designed to handle concurrent reads. (S3 can handle very high read concurrency, though in extreme cases AWS may impose request rate limits per prefix; if we approach those, we could adopt strategies like partitioning data into multiple prefixes or enabling S3 Enhanced Throughput mode, etc.) The combination of load-balanced stateless pods + K8s autoscaling gives the system the elasticity needed for burst tolerance.

Memory and I/O Management

High performance at scale requires careful memory and I/O management in the service:

Memory usage and limits: DuckDB is an in-process DB that will use memory for caches, query processing, and result buffering. We should set a memory limit for the container (e.g. if the node has 16GB, maybe each pod uses up to 4GB). DuckDB can be configured with a memory limit as well (via PRAGMA memory_limit), or it will by default use all available memory. It’s wise to configure this so that one heavy query doesn’t OOM the pod. For example, if multiple queries run concurrently, each should ideally get a fraction of the memory. The Arrow IPC streaming means we don’t have to accumulate the entire result in memory – we can start sending batches as soon as they are produced, freeing them after sending. Many web frameworks support streaming responses (writing chunks to the socket gradually), which we will use to avoid large in-memory responses.

Result size considerations: If some queries can return very large result sets, we might need to impose limits (either row limits or by using pagination techniques). However, given the binary Arrow format, sending even millions of rows is feasible; the bottleneck will be network bandwidth. In a high-QPS environment, though, extremely large results are problematic (they’d consume a lot of memory and bandwidth and slow others). It may be acceptable to have an API contract that queries return reasonably sized datasets (or encourage filtering to narrow results). If truly large exports are needed, a separate mechanism (like an asynchronous export job) could be better.

I/O throughput and S3 access: With many queries hitting S3, ensure the nodes running the service have good network bandwidth (e.g. AWS EC2 instances with high network throughput). Since our use-case is read-heavy analytics, enabling HTTP keep-alive for S3 and reusing connections is important. DuckDB’s S3 access layer (through httpfs) will reuse connections by default (it uses libcurl under the hood, which can keep connections open). We should also tune the max parallel requests – DuckDB can issue multiple reads in parallel, but we might not want to overload S3 with too many simultaneous requests from one process. It’s a balance: too few and queries are slow, too many and S3 (or intermediate network) could throttle. Empirical testing will help find the sweet spot for number of threads per query.

Co-location: To reduce I/O latency, deploy the service in the same region as the S3 bucket. This avoids high latency on each S3 call. The DuckDB team’s benchmark setup explicitly put the compute in the same AWS region as the data for optimal performance
duckdb.org
. Also, consider enabling S3 Transfer Acceleration or using CloudFront if the data is accessed globally, but if both the clients and the service are in the same region, direct S3 access is usually fastest.

Hot data caching: As mentioned, if a small subset of files are frequently queried, those files will naturally get cached in memory (OS page cache on each node). If we notice certain Parquet files are “hot”, another approach could be to proactively cache them. One could even load them into a local DuckDB table or use DuckDB’s CACHE operator (DuckDB has a CACHE TABLE command for caching table data in memory). However, since our data is on S3 and potentially large, caching entire tables might not be feasible. Relying on the combination of DuckDB’s metadata cache + OS disk cache is a simpler approach that should be effective. If needed, we could run a cron job that touches the hot files to keep them in cache.

Monitoring and tuning: At 100 QPS, even small inefficiencies become noticeable. We should monitor metrics like:

CPU usage per query, memory consumption, S3 bytes read per query, query latency, etc. This will help identify bottlenecks (e.g. if CPU is maxed out, or if queries are I/O-bound waiting on S3).

If CPU is saturated, we might increase the cluster size or reduce per-query thread usage (to allow more queries in parallel rather than one query using all cores).

If I/O is the bottleneck (e.g. high S3 wait times), we might consider enabling compression on Arrow data to reduce network transfer. The Arrow IPC format can optionally compress buffers (in newer versions, Arrow supports LZ4 or ZSTD compression for record batch buffers). DuckDB’s Arrow writer would need to be configured for that if available. Alternatively, at the HTTP layer, enabling HTTP compression (gzip) for responses could reduce bandwidth – though Arrow is already columnar and often contains compressed data internally (Parquet is compressed, but when we send Arrow, we decompress Parquet and send raw column data which might be compressible). If network bandwidth becomes a limiting factor, compressing the Arrow stream might help at the cost of extra CPU.

Graceful degradation: Under very high load, the system should fail gracefully – e.g., return errors or throttle rather than hang. Implement HTTP 429 “Too Many Requests” if the service is beyond capacity, to signal clients to back off or retry. Timeouts should be set so that a stuck query (if one occurs) doesn’t tie up resources forever. Each DuckDB query can be wrapped with a timeout setting (there’s no built-in DuckDB timeout, but the application thread can cancel if it exceeds X seconds). This prevents backlog during spikes.

In summary, memory and I/O management boils down to keeping the working set in memory as much as possible and not overcommitting resources. By using streaming and caching, we minimize redundant I/O and memory copies. Each query will use memory for its operation and free it quickly, and thanks to Arrow streaming, large results don’t all sit in RAM at once. Proper configuration of resources and careful scaling will ensure the system remains stable and performant even under heavy load.

Avoiding Arrow Flight (and Other Complexities)

One explicit requirement was to avoid using Arrow Flight on the client side, and to consider if it’s needed on the server side. As discussed earlier, Arrow Flight is a specialized RPC protocol that uses gRPC for exchanging Arrow data. In our design, Arrow Flight is not necessary at all. We achieve the same goals (high-throughput transfer of Arrow-formatted data) using simpler means. The reasons to stick with plain HTTP + Arrow IPC include:

Simplicity and Compatibility: Any HTTP client can call the API and get Arrow data. They don’t need an Arrow Flight client or libraries. This makes the service easier to use and integrate (for example, a user can hit it with curl or a browser, which wouldn’t be possible with Flight gRPC without custom client code).

No persistent connections needed: Arrow Flight typically keeps a gRPC connection alive and can multiplex queries. Our scenario expects short, independent queries – there’s no need for a long-lived session or bidirectional streaming beyond the HTTP response. Each query’s result can be sent in one shot. The overhead of setting up gRPC and managing Flight sessions would likely outweigh any minor performance benefit for our use case. In fact, the data payload in both cases is Arrow IPC, so the difference is mainly transport protocol and setup.

Lower overhead per request: Flight might shine when transferring extremely large datasets or when a client needs to fetch data in chunks interactively. But for our pattern (client issues query, gets result), HTTP/1.1 or HTTP/2 streaming is perfectly fine. We avoid the additional Protobuf wrapping that Flight uses for its messages
arrow.apache.org
. As one Hacker News discussion noted, Arrow Flight embeds Arrow data in gRPC but Arrow’s own protocol is already efficient without it. By not using Flight, we also avoid running a gRPC server alongside our HTTP server – one less thing to maintain.

Server-side complexity: If we tried to use Arrow Flight on the server, we would need to implement the Flight SQL protocol (so that clients could send a SQL query and get Arrow back). DuckDB actually has some support for Flight (DuckDB can act as a Flight SQL server), but enabling that would likely conflict with our goal of simple stateless HTTP. Flight would want clients to connect via Flight and send queries, which isn’t how our web API is intended to be used. We’d end up effectively duplicating an API – an HTTP one and a Flight one – which is unnecessary overhead. Instead, we focus on one API (HTTP+REST) that everyone will use.

In summary, Arrow Flight is not used in this design because it’s not needed for achieving high throughput. We rely on the Arrow IPC streaming format over HTTP, which is proven to be efficient and is simpler to deploy. This aligns with the requirement to avoid client-side Flight and suggests that on the server we don’t benefit from adding it either. As the Stack Overflow comment hinted, it’s straightforward to send Arrow over HTTP without Flight
stackoverflow.com
. By avoiding extraneous protocols and layers, we reduce complexity and potential points of failure, which ultimately improves reliability at scale.

Conclusion and Key Recommendations

Building a high-throughput HTTP service on DuckDB + Delta + Arrow is feasible and can deliver excellent performance if designed correctly. By combining DuckDB’s efficient query engine (with Delta Lake optimizations) with the compact Arrow streaming format, we can serve analytical queries on cloud data at scale. Below are the key design strategies and recommendations drawn from the research:

Use DuckDB’s strengths (and mitigate its weaknesses): Leverage DuckDB’s columnar speed, parallel execution, and Delta Lake integration (pruning, pushdown) to minimize how much data each query reads
duckdb.org
duckdb.org
. At the same time, recognize that DuckDB is not tuned for high TPS on tiny transactions
duckdb.org
 – thus use connection pooling, avoid reconnecting for each query, and feed it vectorized work (e.g., scanning a few thousand rows is better than 1 row at a time).

Parametric Query API (no ad-hoc SQL): Design the HTTP endpoints around fixed query patterns with filter parameters. This makes the API simple and safe, and allows internal optimization like prepared statements. The client experience is improved (they don’t need SQL) and the server has full control over query structure.

Arrow IPC streaming output: Always return data in the Arrow IPC stream format over HTTP. This yields high throughput and low serialization overhead, as Arrow is designed for fast in-memory data exchange
stackoverflow.com
. The use of application/vnd.apache.arrow.stream is already demonstrated in similar systems
github.com
. Clients can parse it easily using Arrow libraries, and we avoid heavy JSON encoding/decoding.

Avoid Arrow Flight – keep it HTTP: We satisfy the data streaming need without introducing Arrow Flight. This avoids the complexity of gRPC and stays in line with stateless HTTP design. Arrow data can be sent directly over HTTP with no loss of performance
stackoverflow.com
.

Metadata caching and warm startup: Load the DuckDB delta extension and attach the Delta table on service startup. This way, metadata (file lists, statistics) is cached in RAM and reused on every query
duckdb.org
. It significantly reduces overhead per query. If possible, pin the snapshot if data is read-only or updates are infrequent, for maximum reuse of metadata
duckdb.org
. Ensure new instances do this initialization before joining the load balancer (you can use readiness probes to only accept traffic after the attach is done).

Horizontal scalability via Kubernetes: Run the service on Kubernetes and enable autoscaling. Use HPA for CPU-based scaling and/or KEDA for event-based scaling to handle bursts gracefully. This ensures the service can serve >100 QPS by distributing load across many pods. Crucially, scaling out is the solution for high QPS – do not try to push a single DuckDB process to handle everything. Keep each instance’s load at a reasonable level and let the cluster scale out for more throughput.

Tune concurrency and parallelism: Within each instance, allow some concurrency (multiple threads or async tasks querying DuckDB). But control it – e.g. limit to a number of concurrent queries that the CPU cores can handle. This prevents overload and keeps latency low. DuckDB will also use intra-query parallelism; you might cap the threads per query if you expect many simultaneous queries, to avoid fighting for CPU.

Resource and I/O optimization: Co-locate compute with data (same region as S3), use efficient instance types (with good network and disk I/O). Monitor S3 usage – if a particular query pattern causes lots of S3 reads, consider caching those results or data. Use compression on transfers if bandwidth becomes an issue. Also, set appropriate memory limits – DuckDB will happily use RAM for caching, but in a container environment you want to avoid OOM kills.

Robustness and simplicity: Keep the system simple by using proven components (HTTP server, DuckDB, Arrow) without adding extra layers. Implement proper error handling – e.g., if DuckDB throws an error (bad parameter, etc.), return an HTTP 400/500 with a useful message. Make sure to handle timeouts and cancellations (if a client disconnects, abort the DuckDB query to save work). These help in a high-QPS scenario to not tie up resources unnecessarily.

By following these guidelines, the resulting service will be highly performant (thanks to vectorized in-process querying and zero-copy data returns) and scalable (thanks to stateless design and autoscaling). DuckDB’s ability to push down filters to S3 and skip irrelevant data means even large 𝑛 terabyte tables can be queried on the fly with low latency, as long as queries are selective
duckdb.org
duckdb.org
. The Arrow streaming output ensures that the throughput of delivering results to clients is maximized, with minimal transformation overhead. Together, these allow serving many users querying an S3 data lake concurrently, while maintaining interactive speeds.

In conclusion, a well-architected DuckDB+Arrow API service can combine the analytical power of a columnar database with the scalability of cloud infrastructure. It avoids undue complexity (no separate query servers, no client-side SQL or drivers) and focuses on efficiency at every layer. This aligns with the goal of “delivering strong performance and stability at scale while avoiding complexity.” With proper tuning and autoscaling, the service will handle 100+ QPS bursts, making data on a data lake available to users quickly and reliably through a simple HTTP interface.