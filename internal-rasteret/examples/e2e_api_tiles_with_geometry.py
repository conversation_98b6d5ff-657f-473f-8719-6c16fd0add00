# SPDX-FileCopyrightText: Terrafloww Labs, 2025

from __future__ import annotations

import asyncio
import os
from typing import Optional

import numpy as np
from shapely.geometry import Polygon
from shapely import wkb as shapely_wkb
import aiohttp
import time

from rasteret.api_client import fetch_scene_assets, read_tiles_from_asset_row


# Bangalore polygon from basic_workflow_xarray.py
BANGALORE_POLY = Polygon(
    [
        (77.55, 13.01),
        (77.58, 13.01),
        (77.58, 13.08),
        (77.55, 13.08),
        (77.55, 13.01),
    ]
)


def to_wkb_hex(poly: Polygon) -> str:
    return shapely_wkb.dumps(poly, hex=True)


async def _wait_for_api(base_url: str, endpoint: str = "/v1/datasets", timeout_s: float = 30.0) -> None:
    """Poll a lightweight public endpoint until API is reachable or timeout.
    Avoids racing the server startup and gives clearer errors.
    """
    url = f"{base_url.rstrip('/')}{endpoint}"
    start = time.time()
    last_err: Exception | None = None
    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5.0)) as session:
        while time.time() - start < timeout_s:
            try:
                async with session.get(url) as resp:
                    if resp.status < 500:
                        return
            except Exception as e:
                last_err = e
            await asyncio.sleep(0.5)
    raise RuntimeError(f"API not reachable at {url} after {timeout_s:.1f}s: {type(last_err).__name__ if last_err else 'Timeout'}")


async def run(
    *,
    base_url: str,
    dataset: str = "sentinel-2-l2a",
    date_start: str = "2025-01-01",
    date_end: str = "2025-01-25",
    assets: str = "red,green,blue",
    limit: int = 8,
) -> None:
    """
    E2E with geometry filtering:
    - Sends geometry_wkb (hex) for Bangalore AOI
    - Reads tiles for 1–2 assets and applies geometry mask client-side
    - Measures timings of stages and compares S2 vs bbox fallback
    """
    # Ensure API is ready
    await _wait_for_api(base_url)

    geom_hex = to_wkb_hex(BANGALORE_POLY)

    # Test S2 filtering (first date range)
    print("=== S2 FILTERING TEST ===")
    t_s2_start = time.time()
    table_s2, headers_s2 = await fetch_scene_assets(
        base_url,
        dataset=dataset,
        date_start=date_start,
        date_end=date_end,
        assets=assets,
        geometry_wkb=geom_hex,
        limit=limit,
        s2_disable=False
    )
    t_s2_ms = (time.time() - t_s2_start) * 1000.0

    print(f"S2 API call: {t_s2_ms:.1f}ms")
    print(f"S2 headers: time_ms={headers_s2.get('x-query-time-ms')} rows={headers_s2.get('x-rows-returned')} bytes={headers_s2.get('x-bytes-scanned')}")

    print(f"S2 result: {len(table_s2)} rows")
    print()

    # Test BBOX-only filtering (different date range to avoid cache)
    print("=== BBOX-ONLY FILTERING TEST ===")
    bbox_date_start = "2025-01-05"  # Different date range
    bbox_date_end = "2025-02-10"
    t_bbox_start = time.time()
    table_bbox, headers_bbox = await fetch_scene_assets(
        base_url,
        dataset=dataset,
        date_start=bbox_date_start,
        date_end=bbox_date_end,
        assets=assets,
        geometry_wkb=geom_hex,
        limit=limit,
        s2_disable=True,
    )
    t_bbox_ms = (time.time() - t_bbox_start) * 1000.0

    print(f"BBOX API call: {t_bbox_ms:.1f}ms")
    print(f"BBOX headers: time_ms={headers_bbox.get('x-query-time-ms')} rows={headers_bbox.get('x-rows-returned')} bytes={headers_bbox.get('x-bytes-scanned')}")

    print(f"BBOX result: {len(table_bbox)} rows")
    print()

    # Use S2 results for COG reading test
    table = table_s2
    headers = headers_s2
    t_api_ms = t_s2_ms

    # Summary comparison
    print("=== COMPARISON SUMMARY ===")
    print(f"S2 vs BBOX timing:")
    print(f"  S2:   {t_s2_ms:.1f}ms client, {headers_s2.get('x-query-time-ms')}ms server, {len(table_s2)} rows")
    print(f"  BBOX: {t_bbox_ms:.1f}ms client, {headers_bbox.get('x-query-time-ms')}ms server, {len(table_bbox)} rows")
    print()

    rows = table.to_pylist()
    if not rows:
        raise RuntimeError("API returned no rows for the given parameters + geometry")

    # Measure COG reads + masking
    print("=== COG READING TEST ===")
    t_reads_start = time.time()
    success = 0
    for idx, row in enumerate(rows[:limit]):
        try:
            arr, _transform = await read_tiles_from_asset_row(
                row,
                geometry=BANGALORE_POLY,
                max_concurrent=64,
                debug=False,
            )
            if arr is None or arr.size == 0:
                raise RuntimeError("Empty masked array")
            if not isinstance(arr, np.ndarray):
                raise RuntimeError(f"Unexpected array type: {type(arr)}")
            print(f"Row {idx}: masked tiles shape={arr.shape}, dtype={arr.dtype}")
            success += 1
        except Exception as e:
            # Minimal, non-sensitive error details
            print(f"Row {idx}: ERROR: {e.__class__.__name__}: {e}")
            raise

    t_reads_ms = (time.time() - t_reads_start) * 1000.0

    # Final summary
    print()
    print("=== FINAL SUMMARY ===")
    print(f"COG reads: {t_reads_ms:.1f}ms for {success} assets")
    print(f"Total E2E: S2={t_s2_ms + t_reads_ms:.1f}ms, BBOX={t_bbox_ms:.1f}ms (different dates)")

    if success == 0:
        raise RuntimeError("No tiles read successfully")


def main() -> None:
    base_url: str = os.getenv("RASTERET_API_BASE_URL", "http://127.0.0.1:8099")
    dataset: str = os.getenv("RASTERET_DATASET", "sentinel-2-l2a")
    assets: str = os.getenv("RASTERET_ASSETS", "red,green,blue")
    date_start: str = os.getenv("RASTERET_DATE_START", "2025-01-10")
    date_end: str = os.getenv("RASTERET_DATE_END", "2025-02-15")
    limit: int = int(os.getenv("RASTERET_LIMIT", "8"))

    asyncio.run(
        run(
            base_url=base_url,
            dataset=dataset,
            date_start=date_start,
            date_end=date_end,
            assets=assets,
            limit=limit,
        )
    )


if __name__ == "__main__":
    main()

