{"cells": [{"cell_type": "markdown", "id": "63b44b00", "metadata": {}, "source": ["# Rasteret example notebook that gives Xarray outputs\n", "\n"]}, {"cell_type": "code", "execution_count": 1, "id": "259da3f2", "metadata": {}, "outputs": [], "source": ["import nest_asyncio\n", "from pathlib import Path\n", "from shapely.geometry import Polygon\n", "from rasteret import Rasteret\n", "from rasteret.constants import DataSources\n", "import xarray as xr\n", "\n", "# Import necessary libraries and apply nest_asyncio\n", "nest_asyncio.apply()\n"]}, {"cell_type": "code", "execution_count": 2, "id": "d6cf7929-caed-44c2-825a-b58d5b7bec94", "metadata": {}, "outputs": [], "source": ["\n", "workspace_dir = Path.home() / \"rasteret_workspace\"\n", "workspace_dir.mkdir(exist_ok=True)\n", "\n", "# Define a custom name for the stac index\n", "custom_name = \"bangalore-v2\"\n", "\n", "# Define area, time range and data source required in stac search\n", "date_range = (\"2024-12-01\", \"2025-01-30\")\n", "data_source = DataSources.LANDSAT  # or SENTINEL2\n", "\n", "aoi1_polygon = Polygon(\n", "    [(77.55, 13.01), (77.58, 13.01), (77.58, 13.08), (77.55, 13.08), (77.55, 13.01)]\n", ")\n", "\n", "aoi2_polygon = Polygon(\n", "    [(77.56, 13.02), (77.59, 13.02), (77.59, 13.09), (77.56, 13.09), (77.56, 13.02)]\n", ")\n", "\n", "# get total bounds of all polygons above for stac search and stac index creation\n", "bbox = aoi1_polygon.union(aoi2_polygon).bounds\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "9bda8dfe-ab84-45ef-83b8-a79787e63737", "metadata": {}, "outputs": [], "source": ["# 1. Search for already created collections\n", "print(\"1. Listing Available Collections\")\n", "print(\"--------------------------\")\n", "collections = Rasteret.list_collections(workspace_dir=workspace_dir)\n", "for c in collections:\n", "    print(f\"- {c['name']}: {c['data_source']}, {c['date_range']}, {c['size']} scenes\")"]}, {"cell_type": "code", "execution_count": null, "id": "0d5e7c67-3ccd-42ed-9b2f-4fce08197d66", "metadata": {}, "outputs": [], "source": ["# You can load any existing collection in any folder\n", "processor = Rasteret.load_collection(\"bangalore_202401-01_landsat\", workspace_dir=workspace_dir)\n"]}, {"cell_type": "code", "execution_count": null, "id": "3328b68d-1a80-4ba6-8f4c-b665ab9145b6", "metadata": {}, "outputs": [], "source": ["print(\"2. Creating New Collection with custom name :\", custom_name)\n", "# Create new collection\n", "processor = Rasteret(\n", "    custom_name=custom_name,\n", "    data_source=data_source,\n", "    workspace_dir=workspace_dir,\n", "    date_range=date_range\n", ")\n", "processor.create_collection(\n", "    bbox=bbox,\n", "    date_range=date_range,\n", "    cloud_cover_lt=20,\n", "    platform={\"in\": [\"LANDSAT_8\"]}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e7546296-9d56-4b2e-8720-98dbb3e29f39", "metadata": {}, "outputs": [], "source": ["# With the processor object, you can now proceed to process the data with \n", "# as many geometries as you want and also choose the bands you want to process data for\n", "\n", "print(\"\\n3. Processing Data\")\n", "print(\"-----------------\")\n", "ds = processor.get_xarray(\n", "    geometries=[aoi1_polygon, aoi2_polygon],\n", "    bands=[\"B4\", \"B5\"],\n", "    cloud_cover_lt=20\n", ")\n", "\n", "# returns an xarray dataset with the data for the geometries and bands specified\n"]}, {"cell_type": "code", "execution_count": 7, "id": "aa8f4c96-5c5f-4847-a6f3-1a1a06257312", "metadata": {}, "outputs": [], "source": ["# Calculate NDVI\n", "ndvi = (ds.B5 - ds.B4) / (ds.B5 + ds.B4)\n", "\n", "# Create a new dataset with NDVI as a variable\n", "ndvi_ds = xr.Dataset(\n", "    {\"NDVI\": ndvi},\n", "    coords=ds.coords,\n", "    attrs=ds.attrs\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1ffa2f14-d80d-4071-a657-838ab2e7f9c9", "metadata": {}, "outputs": [], "source": ["from rasteret.core.utils import save_per_geometry\n", "\n", "# Save results in any directory\n", "output_dir = Path(f\"ndvi_results_{custom_name}\")\n", "output_dir.mkdir(exist_ok=True)\n", "\n", "# Save the NDVI data for each geometry in a separate file\n", "# provide file_prefix to name the files, and the xarray variable name to save\n", "output_files = save_per_geometry(ndvi_ds, output_dir, file_prefix=\"ndvi\", data_var=\"NDVI\")\n", "\n", "print(\"\\nProcessed NDVI files:\")\n", "for geom_id, filepath in output_files.items():\n", "    print(f\"Geometry {geom_id}: {filepath}\")\n"]}], "metadata": {"kernelspec": {"display_name": "env11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0rc1"}}, "nbformat": 4, "nbformat_minor": 5}