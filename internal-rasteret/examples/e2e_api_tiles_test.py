# SPDX-FileCopyrightText: Terrafloww Labs, 2025

from __future__ import annotations

import asyncio
import os
from typing import Optional

import numpy as np

from rasteret.api_client import fetch_scene_assets, read_tiles_from_asset_row


async def run(
    *,
    base_url: str,
    dataset: str = "sentinel-2-l2a",
    date_start: str = "2025-01-10",
    date_end: str = "2025-02-15",
    assets: str = "red,green,blue",
    limit: int = 2,
) -> None:
    """
    Minimal end-to-end smoke test against Arrow IPC API.
    - Fetch a few asset rows and read tiles for 1–2 assets.
    - Prints tile array shapes/dtypes; raises on failure.
    """
    table = await fetch_scene_assets(
        base_url,
        dataset=dataset,
        date_start=date_start,
        date_end=date_end,
        assets=assets,
        limit=limit,
    )

    rows = table.to_pylist()
    if not rows:
        raise RuntimeError("API returned no rows for the given parameters")

    success = 0
    for idx, row in enumerate(rows[:limit]):
        # Read tiles without geometry for a simple first check
        arr, _transform = await read_tiles_from_asset_row(row, geometry=None, max_concurrent=64, debug=False)
        if arr is None or arr.size == 0:
            raise RuntimeError(f"Empty array for row {idx}")
        if not isinstance(arr, np.ndarray):
            raise RuntimeError(f"Unexpected array type for row {idx}: {type(arr)}")
        print(f"Row {idx}: tiles shape={arr.shape}, dtype={arr.dtype}")
        success += 1

    if success == 0:
        raise RuntimeError("No tiles read successfully")


def main() -> None:
    base_url: str = os.getenv("RASTERET_API_BASE_URL", "http://127.0.0.1:8099")
    # Allow overriding dataset or assets via env if needed
    dataset: str = os.getenv("RASTERET_DATASET", "sentinel-2-l2a")
    assets: str = os.getenv("RASTERET_ASSETS", "red,green,blue")
    date_start: str = os.getenv("RASTERET_DATE_START", "2025-01-10")
    date_end: str = os.getenv("RASTERET_DATE_END", "2025-02-15")
    limit: int = int(os.getenv("RASTERET_LIMIT", "2"))

    asyncio.run(
        run(
            base_url=base_url,
            dataset=dataset,
            date_start=date_start,
            date_end=date_end,
            assets=assets,
            limit=limit,
        )
    )


if __name__ == "__main__":
    main()

