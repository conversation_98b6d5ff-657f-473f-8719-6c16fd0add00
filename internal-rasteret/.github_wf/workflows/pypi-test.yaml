# name: Publish to Test PyPI

# on:
#   push:
#     tags:
#       - 'v*.*.*'

# jobs:
#   build:
#     runs-on: ubuntu-latest

#     steps:
#     - name: Checkout repository
#       uses: actions/checkout@v4

#     - name: Set up Python
#       uses: actions/setup-python@v4
#       with:
#         python-version: "3.11"

#     - name: Install pypa/build
#       run: python3 -m pip install build --user

#     - name: Build a binary wheel and a source tarball
#       run: python3 -m build

#     - name: Store the distribution packages
#       uses: actions/upload-artifact@v2
#       with:
#         name: python-package-distributions
#         path: dist/

#   publish-to-testpypi:
#     name: Publish Python 🐍 distribution 📦 to Test PyPI
#     if: startsWith(github.ref, 'refs/tags/')  # only publish to PyPI on tag pushes
#     needs: build
#     runs-on: ubuntu-latest
#     environment:
#       name: testpypi
#       url: https://testpypi.org/project/rasteret
#     permissions:
#       id-token: write
#     steps:
#     - name: Download all the dists
#       uses: actions/download-artifact@v4
#       with:
#         name: python-package-distributions
#         path: dist/
#     - name: Publish distribution 📦 to PyPI
#       uses: pypa/gh-action-pypi-publish@release/v1