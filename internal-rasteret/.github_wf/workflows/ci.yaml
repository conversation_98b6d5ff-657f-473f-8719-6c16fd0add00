# name: CI
# on:
#   push:
#     branches:
#       - main
#     paths:
#       - requirements.txt
#       - 'src/**'
#       - 'examples/**'
#   pull_request:
#     branches:
#       - main
#     paths:
#       - requirements.txt
#       - 'src/**'
#       - 'examples/**'

# jobs:
#   build:
#     runs-on: ubuntu-latest

#     strategy:
#       matrix:
#         python-version: ["3.11"]

#     steps:
#     - name: Checkout repository
#       uses: actions/checkout@v4

#     - name: Set up Python ${{ matrix.python-version }}
#       uses: actions/setup-python@v4
#       with:
#         python-version: ${{ matrix.python-version }}

#     - name: Install dependencies
#       run: |
#         python -m pip install --upgrade pip
#         pip install uv
#         uv pip install -r requirements.txt --system
#         pip install -e .[dev]

#     - name: Lint with ruff
#       run: |
#         ruff check .

#     - name: Format with black
#       run: |
#         black --check .

#     - name: Run tests
#       run: |
#         pytest --cov=rasteret