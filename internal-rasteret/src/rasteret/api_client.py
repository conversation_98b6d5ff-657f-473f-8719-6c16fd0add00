# SPDX-FileCopyrightText: Terrafloww Labs, 2025

from __future__ import annotations

import io
from typing import Any, Dict, Iterator, List, Mapping, Optional, Tuple

import aiohttp
import pyarrow as pa
import pyarrow.ipc as ipc
import numpy as np

from rasteret.types import CogMetadata
from rasteret.fetch.cog import read_cog_tile_data
from rasteret.constants import STAC_COLLECTION_BAND_MAPS

ARROW_MIME = "application/vnd.apache.arrow.stream"


def _normalize_assets(dataset: str, assets: str) -> str:
    """Normalize assets parameter using band alias map for the dataset.

    - Accepts comma-separated values (e.g., "B02,B03,red,nir")
    - If a token matches a band code key (e.g., B02) for the dataset, map to canonical cog_key (e.g., blue)
    - Otherwise, keep the token as-is (so canonical names like 'blue' continue to work)
    - Case-insensitive for codes; output canonical names in lower-case as per table
    """
    band_map = (
        STAC_COLLECTION_BAND_MAPS.get(dataset)
        or STAC_COLLECTION_BAND_MAPS.get(dataset.lower())
        or STAC_COLLECTION_BAND_MAPS.get(dataset.upper())
        or {}
    )
    # Normalize keys for case-insensitive lookup (e.g., B8a -> B8A)
    upper_key_map = {k.upper(): v for k, v in band_map.items()}

    tokens = [t.strip() for t in assets.split(",") if t.strip()]
    normalized: List[str] = []
    for t in tokens:
        # Try band code mapping first (e.g., B02, B8A, B11)
        mapped = upper_key_map.get(t.upper())
        if mapped:
            normalized.append(mapped)
        else:
            # Keep original token; backend expects canonical names in lower-case already, but
            # if user supplied upper, leave it—they may be passing a custom key
            normalized.append(t)
    return ",".join(normalized)


async def _arrow_from_aiohttp_stream(resp: aiohttp.ClientResponse) -> tuple[pa.Table, dict[str, str]]:
    """Read an Arrow IPC stream and return (table, headers)."""
    if resp.status >= 400:
        text = await resp.text()
        raise RuntimeError(f"HTTP {resp.status}: {text}")
    buf = io.BytesIO()
    async for chunk in resp.content.iter_chunked(65536):
        buf.write(chunk)
    buf.seek(0)
    reader = ipc.open_stream(buf)
    table = reader.read_all()
    headers = {k: v for k, v in resp.headers.items()}
    return table, headers


async def fetch_scenes(
    base_url: str,
    *,
    dataset: str,
    date_start: str,
    date_end: str,
    bbox: Optional[str] = None,
    geometry_wkb: Optional[str] = None,
    cloud_cover_max: Optional[float] = None,
    limit: int = 10000,
    offset: int = 0,
    headers: Optional[Dict[str, str]] = None,
    timeout: float = 60.0,
) -> pa.Table:
    """Fetch scenes as a PyArrow table from Terrafloww Data API.

    Params mirror the API. Results are capped server-side and safe to buffer.
    """
    params: Dict[str, Any] = {
        "dataset": dataset,
        "date_start": date_start,
        "date_end": date_end,
        "limit": limit,
        "offset": offset,
    }
    if bbox:
        params["bbox"] = bbox
    if geometry_wkb:
        params["geometry_wkb"] = geometry_wkb
    if cloud_cover_max is not None:
        params["cloud_cover_max"] = cloud_cover_max

    req_headers = {"Accept": ARROW_MIME}
    if headers:
        req_headers.update(headers)

    url = f"{base_url.rstrip('/')}/v1/scenes"
    timeout_cfg = aiohttp.ClientTimeout(total=timeout)
    async with aiohttp.ClientSession(timeout=timeout_cfg) as session:
        async with session.get(url, params=params, headers=req_headers) as resp:
            return await _arrow_from_aiohttp_stream(resp)


async def fetch_scene_assets(
    base_url: str,
    *,
    dataset: str,
    date_start: str,
    date_end: str,
    assets: Optional[str] = None,
    bbox: Optional[str] = None,
    geometry_wkb: Optional[str] = None,
    cloud_cover_max: Optional[float] = None,
    limit: int = 10000,
    offset: int = 0,
    s2_disable: bool = False,
    explain: bool = False,
    headers: Optional[Dict[str, str]] = None,
    timeout: float = 60.0,
) -> tuple[pa.Table, Dict[str, str]]:
    """Fetch per-asset rows (with cog_* fields) as a PyArrow table and return response headers."""
    params: Dict[str, Any] = {
        "dataset": dataset,
        "date_start": date_start,
        "date_end": date_end,
        "limit": limit,
        "offset": offset,
    }
    if assets:
        params["assets"] = _normalize_assets(dataset, assets)
    if bbox:
        params["bbox"] = bbox
    if geometry_wkb:
        params["geometry_wkb"] = geometry_wkb
    if cloud_cover_max is not None:
        params["cloud_cover_max"] = cloud_cover_max
    if s2_disable:
        params["s2_disable"] = "true"
    if explain:
        params["explain"] = "true"

    req_headers = {"Accept": ARROW_MIME}
    if headers:
        req_headers.update(headers)

    url = f"{base_url.rstrip('/')}/v1/scenes:assets"
    timeout_cfg = aiohttp.ClientTimeout(total=timeout)
    async with aiohttp.ClientSession(timeout=timeout_cfg) as session:
        async with session.get(url, params=params, headers=req_headers) as resp:
            return await _arrow_from_aiohttp_stream(resp)


# ---- Rasteret integration helpers ----

def _dtype_from_codes(dtype_code: Optional[int], bits: Optional[int]) -> np.dtype:
    """Minimal mapping for common EO dtypes; extend as needed."""
    key = (int(dtype_code) if dtype_code is not None else None, int(bits) if bits is not None else None)
    mapping = {
        (1, 8): np.uint8,
        (1, 16): np.uint16,
        (2, 8): np.int8,
        (2, 16): np.int16,
        (3, 32): np.float32,
    }
    return mapping.get(key, np.uint16)





def to_cog_metadata(row: Mapping[str, Any]) -> CogMetadata:
    """Map an asset row (from /v1/scenes:assets) to rasteret CogMetadata.

    Expects keys like: cog_width, cog_height, cog_tile_width, cog_tile_height,
    cog_crs_code, transform (4-el) or cog_transform (6-el), cog_tile_offsets,
    cog_tile_byte_counts, cog_predictor, cog_compression_code, cog_dtype_code, cog_bits_per_sample.
    """
    # API provides 4-element "transform" field in rasteret format
    transform4 = row.get("transform")

    dtype = _dtype_from_codes(row.get("cog_dtype_code"), row.get("cog_bits_per_sample"))

    return CogMetadata(
        width=int(row["cog_width"]),
        height=int(row["cog_height"]),
        tile_width=int(row["cog_tile_width"]),
        tile_height=int(row["cog_tile_height"]),
        dtype=dtype,
        crs=int(row["cog_crs_code"]),
        predictor=int(row["cog_predictor"]) if row.get("cog_predictor") is not None else None,
        compression=int(row["cog_compression_code"]) if row.get("cog_compression_code") is not None else None,
        transform=transform4,
        tile_offsets=list(row["cog_tile_offsets"]) if row.get("cog_tile_offsets") is not None else None,
        tile_byte_counts=list(row["cog_tile_byte_counts"]) if row.get("cog_tile_byte_counts") is not None else None,
        pixel_scale=tuple(row["pixel_scale"]) if row.get("pixel_scale") is not None else None,
        tiepoint=tuple(row["tiepoint"]) if row.get("tiepoint") is not None else None,
    )


async def read_tiles_from_asset_row(row: Mapping[str, Any], geometry=None, *, max_concurrent: int = 150, debug: bool = False):
    """Convenience wrapper: build CogMetadata from row and read tiles.

    Returns (np.ndarray, Affine|None).
    """
    metadata = to_cog_metadata(row)
    url = row.get("request_url") or row.get("cog_href")
    if url is None:
        raise ValueError("Row missing 'request_url'/'cog_href'")
    return await read_cog_tile_data(url, metadata, geometry=geometry, max_concurrent=max_concurrent, debug=debug)

