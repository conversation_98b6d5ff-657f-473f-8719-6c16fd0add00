[build-system]
requires = ["hatchling>=1.21.0"]
build-backend = "hatchling.build"

[project]
name = "rasteret"
version = "0.1.20"
description = "Fast and efficient access to Cloud-Optimized GeoTIFFs (COGs)"
readme = "README.md"
requires-python = ">=3.10,<3.12"
license = { file = "LICENSE" }
authors = [
  { name = "Sidharth Subramaniam", email = "<EMAIL>" }
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Science/Research",
  "License :: OSI Approved :: Apache Software License",
  "Operating System :: OS Independent",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Topic :: Scientific/Engineering :: GIS",
  "Topic :: Scientific/Engineering :: Image Processing",
]
dependencies = [
  "numpy>=1.24.0",
  "pyarrow>=14.0.1",
  "pystac-client>=0.7.5",
  "stac-geoparquet==0.6.0",
  "aiohttp>=3.9.1",
  "aiofiles>=23.2.1",
  "cachetools>=5.3.2",
  "geoarrow-pyarrow>=0.1.0",
  "geoarrow-pandas>=0.1.0",
  "boto3==1.18.32",
  "xarray==2025.01.0",
  "rioxarray>=0.7.0",
  "imagecodecs>=2023.9.18",
  "zstandard>=0.22.0",
  "pyproj>=3.6.1",
  "shapely>=2.0.2",
  "affine>=2.4.0",
  "rasterio>=1.4.0",
  "tqdm>=4.66.1",
  "rich>=13.7.0",
]

[project.optional-dependencies]
dev = [
  "pytest>=7.4.3",
  "pytest-asyncio>=0.23.2",
  "pytest-cov>=4.1.0",
  "black>=23.12.1",
  "ruff==0.8.6",
  "isort>=5.13.2",
  "flake8>=7.0.0",
  "httpx[http2]",
]

[tool.black]
line-length = 88
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 88

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --cov=rasteret"
testpaths = [
    "tests",
]
