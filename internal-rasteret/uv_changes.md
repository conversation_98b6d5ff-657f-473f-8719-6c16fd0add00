How to use UV now (local dev)
From internal-rasteret:

First-time env sync:
uv sync
Run the E2E API smoke test you asked me to add:
uv run python examples/e2e_api_tiles_test.py
Run tests/linters (once you re-enable or want to run locally):
uv run pytest
uv run ruff check .
uv run black --check .
UV will read dependencies directly from pyproject.toml. requirements.txt is no longer used.

Impact on GitHub workflows
Your “real” workflows directory is currently .github_wf/workflows (not .github/workflows), and all jobs are commented out. That means nothing is running today — so the UV switch has no immediate impact.

When we re-enable CI later, plan to:

Move YAMLs into .github/workflows/
Replace pip steps with UV commands:
Install UV (actions/setup-python + pipx install uv or curl installer)
uv sync
uv run ruff/black/pytest
Example CI steps (for later):
uses: actions/setup-python@v5 with python-version: "3.11"
run: pipx install uv
run: uv sync
run: uv run ruff check .
run: uv run black --check .
run: uv run pytest --cov=rasteret
For now: no change required — everything’s already commented and out of .github/.

PyPI publishing impact (later; disabled now)
Since we moved away from setuptools setup.py:

Build step becomes either:
uv build (preferred with UV), or
python -m build (uses hatchling backend)
Versioning is now in pyproject.toml [project].version (no setuptools_scm here)
Publish step remains via pypa/gh-action-pypi-publish
Example (when we re-enable, not now):

Build:
uv build # produces dist/*.whl and *.tar.gz
Publish:
uses: pypa/gh-action-pypi-publish@release/v1
Since you don’t want to publish now, we’ll keep all PyPI workflows commented/parked.

Minimal, safe migration summary
Project is now UV/pyproject-driven for env and deps (done)
No active CI or PyPI jobs were broken (they’re disabled)
We kept setup.py and requirements.txt to avoid surprises; they’re now redundant. We can remove them after CI is updated to UV.
Next step from you
Run:
uv sync in internal-rasteret
Start your API (as you shared)
uv run python examples/e2e_api_tiles_test.py
If you confirm you don’t need setup.py and requirements.txt anymore, I can remove them in a follow-up PR and add a short README note on UV usage.