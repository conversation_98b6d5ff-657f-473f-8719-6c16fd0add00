# Delta Lake DuckDB API Service

A high-performance API service for querying Delta Lake tables using DuckDB with Apache Arrow streaming responses.

## Features

### Core Functionality
- **DuckDB-powered querying** with delta, httpfs, and cache_httpfs extensions
- **Apache Arrow streaming** for efficient data transfer
- **JSON responses** for smaller datasets and debugging
- **S3 Delta Lake support** with automatic credential management
- **Connection pooling** for concurrent query handling
- **Query safety controls** with whitelisting and resource limits

### Performance & Scalability
- **Concurrent query execution** with configurable limits
- **Result size caps** and timeout controls
- **Object caching** for improved S3 performance
- **Automatic snapshot refresh** for Delta Lake tables
- **Horizontal pod autoscaling** based on query load

### Observability
- **Prometheus metrics** for monitoring query performance
- **Structured logging** for all operations
- **Health checks** and diagnostic endpoints
- **Query execution tracking** with detailed metrics

## Quick Start

### Local Development

1. **Install dependencies**:
```bash
uv sync
```

2. **Set environment variables**:
```bash
export AWS_ACCESS_KEY_ID=your_access_key
export AWS_SECRET_ACCESS_KEY=your_secret_key
export AWS_REGION=us-west-2
```

3. **Run the service**:
```bash
uv run python -m data_marketplace.api.main
```

4. **Access the API**:
- API docs: http://localhost:8000/docs
- Health check: http://localhost:8000/health
- Metrics: http://localhost:8000/metrics

### Docker Deployment

1. **Build the image**:
```bash
docker build -f Dockerfile.api -t delta-lake-api:latest .
```

2. **Run the container**:
```bash
docker run -p 8000:8000 \
  -e AWS_ACCESS_KEY_ID=your_access_key \
  -e AWS_SECRET_ACCESS_KEY=your_secret_key \
  -e AWS_REGION=us-west-2 \
  delta-lake-api:latest
```

### Kubernetes Deployment

1. **Apply the manifests**:
```bash
kubectl apply -f k8s/api-deployment.yaml
```

2. **Configure S3 credentials**:
```bash
kubectl create secret generic s3-credentials \
  --from-literal=access-key-id=your_access_key \
  --from-literal=secret-access-key=your_secret_key
```

## API Usage

### Attach a Delta Lake Table

```bash
curl -X POST "http://localhost:8000/tables/attach" \
  -H "Content-Type: application/json" \
  -d '{
    "alias": "my_table",
    "path": "s3://my-bucket/my-delta-table",
    "pin_snapshot": true
  }'
```

### Execute a Query (JSON Response)

```bash
curl -X POST "http://localhost:8000/q?fmt=json" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT * FROM my_table WHERE date >= {start_date} LIMIT 100",
    "parameters": {
      "start_date": "2024-01-01"
    },
    "timeout": 60.0
  }'
```

### Execute a Query (Arrow Response)

```bash
curl -X POST "http://localhost:8000/q?fmt=arrow" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT * FROM my_table LIMIT 1000"
  }' \
  --output result.arrow
```

### Use Named Queries

```bash
# List available named queries
curl "http://localhost:8000/q/named"

# Execute a named query
curl -X POST "http://localhost:8000/q/named/recent_scenes" \
  -H "Content-Type: application/json" \
  -d '{
    "table_alias": "my_table",
    "since_date": "2024-01-01",
    "limit": 100
  }'
```

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `HOST` | `0.0.0.0` | API server host |
| `PORT` | `8000` | API server port |
| `WORKERS` | `1` | Number of worker processes |
| `DUCKDB_MEMORY_LIMIT` | `4GB` | DuckDB memory limit |
| `DUCKDB_THREADS` | `2` | DuckDB thread count |
| `DUCKDB_CACHE_DIR` | `/tmp/duckdb_cache` | Cache directory |
| `MAX_CONCURRENT_QUERIES` | `8` | Maximum concurrent queries |
| `MAX_RESULT_ROWS` | `1000000` | Maximum rows per result |
| `MAX_RESULT_BYTES` | `104857600` | Maximum bytes per result (100MB) |
| `MAX_QUERY_TIME_SECONDS` | `300` | Maximum query execution time |
| `SNAPSHOT_CHECK_INTERVAL` | `60` | Snapshot refresh check interval |

### S3 Configuration

The service supports multiple authentication methods:

1. **Environment variables**:
   - `AWS_ACCESS_KEY_ID`
   - `AWS_SECRET_ACCESS_KEY`
   - `AWS_REGION`

2. **IAM roles** (when running on AWS)

3. **Credential chain** (default profiles, etc.)

## Monitoring

### Prometheus Metrics

The service exposes metrics at `/metrics`:

- `duckdb_queries_total` - Total queries executed
- `duckdb_query_duration_seconds` - Query execution time
- `duckdb_query_rows_returned` - Rows returned per query
- `duckdb_query_bytes_scanned` - Bytes scanned per query
- `duckdb_queries_in_flight` - Active queries
- `duckdb_attached_tables` - Number of attached tables
- `duckdb_errors_total` - Total errors by type

### Health Checks

- `/health` - Basic health check
- `/monitoring/status` - Detailed service status
- `/monitoring/tables` - Table monitoring status

### Logging

All operations are logged with structured JSON format:

```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "level": "INFO",
  "event_type": "query_complete",
  "query_id": "q_1704110400_1",
  "execution_time_ms": 150.5,
  "rows_returned": 1000,
  "bytes_scanned": 1048576,
  "success": true
}
```

## Performance Tuning

### Memory Configuration

- Set `DUCKDB_MEMORY_LIMIT` to ~75% of available container memory
- Use `DUCKDB_THREADS` = CPU cores / 2 for optimal performance
- Configure cache directory with sufficient disk space

### Concurrency Tuning

- `MAX_CONCURRENT_QUERIES` should be 2-4x CPU cores
- Monitor `duckdb_queries_in_flight` metric
- Adjust HPA settings based on query patterns

### S3 Optimization

- Enable `cache_httpfs` extension for better S3 performance
- Use appropriate S3 regions to minimize latency
- Consider S3 Transfer Acceleration for global access

## Security

### Query Safety

- Only SELECT statements are allowed
- Dangerous keywords are blocked (DROP, DELETE, etc.)
- Query length limits enforced
- Result size limits prevent memory exhaustion

### Network Security

- Run as non-root user in containers
- Use Kubernetes network policies
- Configure TLS termination at load balancer

### Credential Management

- Use Kubernetes secrets for S3 credentials
- Rotate credentials regularly
- Consider using IAM roles instead of static keys

## Troubleshooting

### Common Issues

1. **Out of memory errors**:
   - Reduce `DUCKDB_MEMORY_LIMIT`
   - Lower `MAX_CONCURRENT_QUERIES`
   - Add result size limits

2. **S3 connection failures**:
   - Check credentials and permissions
   - Verify S3 region configuration
   - Test network connectivity

3. **Query timeouts**:
   - Increase `MAX_QUERY_TIME_SECONDS`
   - Optimize query predicates
   - Check Delta table partitioning

### Debug Mode

Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
```

Check service status:
```bash
curl http://localhost:8000/monitoring/status
```

## Testing

Run the integration tests:

```bash
uv run pytest tests/test_api_integration.py -v
```

Load testing with Apache Bench:

```bash
# Test concurrent queries
ab -n 100 -c 10 -T application/json \
  -p query.json \
  http://localhost:8000/q?fmt=json
```

## Contributing

1. Follow the existing code style
2. Add tests for new features
3. Update documentation
4. Ensure all tests pass

## License

Terrafloww Labs Proprietary
