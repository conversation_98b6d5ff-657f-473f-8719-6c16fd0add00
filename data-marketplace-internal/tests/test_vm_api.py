#!/usr/bin/env python3
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
VM-optimized test script for the Delta Lake API.
Tests with realistic timeouts for VM performance.
"""

import asyncio
import io
import time
from datetime import datetime

import httpx
import pyarrow.ipc as ipc


async def test_api_routes(base_url: str) -> None:
    """Test what API routes are actually available."""
    print("🔍 Testing available API routes...")
    
    routes_to_test = [
        "/",
        "/docs",
        "/v1/datasets",
        "/v1/scenes",
        "/v1/scenes:assets",
        "/health",
        "/status"
    ]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        for route in routes_to_test:
            try:
                resp = await client.get(f"{base_url}{route}")
                print(f"   {route}: {resp.status_code}")
                if resp.status_code == 200 and len(resp.text) < 200:
                    print(f"      Response: {resp.text[:100]}...")
            except Exception as e:
                print(f"   {route}: ERROR - {e}")


async def test_basic_connectivity(base_url: str) -> bool:
    """Test basic API connectivity."""
    print("🔌 Testing basic API connectivity...")
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            resp = await client.get(f"{base_url}/v1/datasets")
            if resp.status_code == 200:
                print("✅ API is responding")
                print(f"   Datasets: {resp.json()}")
                return True
            else:
                print(f"❌ API returned status {resp.status_code}")
                print(f"   Response: {resp.text}")
                return False
    except Exception as e:
        print(f"❌ API connectivity failed: {e}")
        return False


async def test_simple_query(base_url: str, timeout: float = 30.0) -> bool:
    """Test a very simple query with JSON response."""
    print("📊 Testing simple query with JSON response...")
    
    params = {
        "dataset": "sentinel-2-l2a",
        "date_start": "2025-01-01",
        "date_end": "2025-01-01",
        "limit": 1,
    }
    
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            # Try JSON first
            resp = await client.get(f"{base_url}/v1/scenes", params=params, headers={"Accept": "application/json"})
            
            print(f"   Status: {resp.status_code}")
            if resp.status_code >= 400:
                print(f"   Error: {resp.text}")
                return False
            
            # Try to parse as JSON
            try:
                data = resp.json()
                print(f"✅ JSON query successful: {len(data)} items")
                if data:
                    print(f"   Sample: {list(data[0].keys())[:5]}...")
                return True
            except:
                print(f"   Response length: {len(resp.content)} bytes")
                print(f"   Content type: {resp.headers.get('content-type')}")
                return True  # Still successful even if not JSON
                
    except Exception as e:
        print(f"❌ Simple query failed: {e}")
        return False


async def test_arrow_query(base_url: str, timeout: float = 30.0) -> bool:
    """Test Arrow IPC response."""
    print("🏹 Testing Arrow IPC query...")
    
    params = {
        "dataset": "sentinel-2-l2a",
        "date_start": "2025-01-01",
        "date_end": "2025-01-01",
        "limit": 1,
    }
    
    headers = {"Accept": "application/vnd.apache.arrow.stream"}
    
    try:
        start_time = time.time()
        async with httpx.AsyncClient(timeout=timeout) as client:
            resp = await client.get(f"{base_url}/v1/scenes", params=params, headers=headers)
            
            if resp.status_code >= 400:
                print(f"❌ Arrow query failed with status {resp.status_code}: {resp.text}")
                return False
            
            # Parse Arrow response
            buf = io.BytesIO(resp.content)
            reader = ipc.open_stream(buf)
            table = reader.read_all()
            
            elapsed = time.time() - start_time
            print(f"✅ Arrow query successful: {len(table)} rows in {elapsed:.2f}s")
            
            if len(table) > 0:
                print(f"   Columns: {table.schema.names[:5]}...")
                print(f"   Sample scene_id: {table['scene_id'][0].as_py()}")
            
            return True
            
    except Exception as e:
        print(f"❌ Arrow query failed: {e}")
        return False


async def test_geometry_query(base_url: str, timeout: float = 60.0) -> bool:
    """Test geometry-based spatial query."""
    print("🌍 Testing geometry query...")
    
    # Small bbox around a known area
    from shapely.geometry import box
    from shapely import wkb
    
    # Small area
    bbox_geom = box(77.5, 12.9, 77.7, 13.1)  # Bangalore area
    geometry_wkb = wkb.dumps(bbox_geom, hex=True)
    
    params = {
        "dataset": "sentinel-2-l2a",
        "date_start": "2024-12-01",
        "date_end": "2024-12-31",
        "geometry_wkb": geometry_wkb,
        "limit": 3,
    }
    
    headers = {"Accept": "application/vnd.apache.arrow.stream"}
    
    try:
        start_time = time.time()
        async with httpx.AsyncClient(timeout=timeout) as client:
            resp = await client.get(f"{base_url}/v1/scenes", params=params, headers=headers)
            
            if resp.status_code >= 400:
                print(f"❌ Geometry query failed with status {resp.status_code}: {resp.text}")
                return False
            
            # Parse Arrow response
            buf = io.BytesIO(resp.content)
            reader = ipc.open_stream(buf)
            table = reader.read_all()
            
            elapsed = time.time() - start_time
            print(f"✅ Geometry query successful: {len(table)} rows in {elapsed:.2f}s")
            
            if len(table) > 0:
                print(f"   Found scenes in Bangalore area")
            
            return True
            
    except Exception as e:
        print(f"❌ Geometry query failed: {e}")
        return False


async def test_assets_query(base_url: str, timeout: float = 60.0) -> bool:
    """Test assets endpoint."""
    print("🖼️  Testing assets query...")
    
    params = {
        "dataset": "sentinel-2-l2a",
        "date_start": "2024-12-15",
        "date_end": "2024-12-16",
        "assets": "red,nir08",
        "limit": 2,
    }
    
    headers = {"Accept": "application/vnd.apache.arrow.stream"}
    
    try:
        start_time = time.time()
        async with httpx.AsyncClient(timeout=timeout) as client:
            resp = await client.get(f"{base_url}/v1/scenes:assets", params=params, headers=headers)
            
            if resp.status_code >= 400:
                print(f"❌ Assets query failed with status {resp.status_code}: {resp.text}")
                return False
            
            # Parse Arrow response
            buf = io.BytesIO(resp.content)
            reader = ipc.open_stream(buf)
            table = reader.read_all()
            
            elapsed = time.time() - start_time
            print(f"✅ Assets query successful: {len(table)} rows in {elapsed:.2f}s")
            
            if len(table) > 0:
                print(f"   Sample cog_key: {table['cog_key'][0].as_py()}")
                print(f"   Sample cog_width: {table['cog_width'][0].as_py()}")
            
            return True
            
    except Exception as e:
        print(f"❌ Assets query failed: {e}")
        return False


async def test_performance_comparison(base_url: str) -> None:
    """Test performance with different query sizes."""
    print("⚡ Testing performance with different limits...")
    
    limits = [1, 5, 10, 50]
    
    for limit in limits:
        params = {
            "dataset": "sentinel-2-l2a",
            "date_start": "2024-12-01",
            "date_end": "2024-12-31",
            "limit": limit,
        }
        
        headers = {"Accept": "application/vnd.apache.arrow.stream"}
        
        try:
            start_time = time.time()
            async with httpx.AsyncClient(timeout=120.0) as client:
                resp = await client.get(f"{base_url}/v1/scenes", params=params, headers=headers)
                
                if resp.status_code == 200:
                    buf = io.BytesIO(resp.content)
                    reader = ipc.open_stream(buf)
                    table = reader.read_all()
                    elapsed = time.time() - start_time
                    
                    print(f"   Limit {limit:2d}: {len(table):2d} rows in {elapsed:5.2f}s ({len(resp.content):,} bytes)")
                else:
                    print(f"   Limit {limit:2d}: FAILED ({resp.status_code})")
                    
        except Exception as e:
            print(f"   Limit {limit:2d}: ERROR - {e}")


async def main():
    """Run all tests."""
    base_url = "http://127.0.0.1:8099"
    
    print("🚀 Testing Optimized Delta Lake API on VM")
    print("=" * 50)
    print(f"API Base URL: {base_url}")
    print(f"Expected: Fast performance on VM")
    print()
    
    # Test 1: Check available routes
    await test_api_routes(base_url)
    print()
    
    # Test 2: Basic connectivity
    if not await test_basic_connectivity(base_url):
        print("❌ Cannot proceed - API is not responding properly")
        return
    print()
    
    # Test 3: Simple query
    if await test_simple_query(base_url):
        print()
    
    # Test 4: Arrow query
    if await test_arrow_query(base_url):
        print()
    
    # Test 5: Geometry query
    if await test_geometry_query(base_url):
        print()
    
    # Test 6: Assets query
    if await test_assets_query(base_url):
        print()
    
    # Test 7: Performance comparison
    await test_performance_comparison(base_url)
    print()
    
    print("=" * 50)
    print("🎯 VM Testing Complete!")


if __name__ == "__main__":
    asyncio.run(main())
