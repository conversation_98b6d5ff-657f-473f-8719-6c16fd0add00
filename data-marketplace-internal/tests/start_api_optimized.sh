#!/bin/bash
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Start the API with the optimized Delta Lake table
# This uses the new unified_stac_table_v2 with 32MB row groups

set -euo pipefail

echo "🚀 Starting Data Marketplace API with Optimized Table"
echo "=================================================="
echo "Table: unified_stac_table_v2 (32MB row groups)"
echo "Port: 8099"
echo "Host: 127.0.0.1"
echo ""

# Set the optimized table path and alias
export API_ATTACH_TABLE_PATH="s3://tf-datalake-bucket/deltalake-tables/unified_stac_table_v2"
export API_ATTACH_TABLE_ALIAS="unified_stac"
# Optional: persist DuckDB catalog for pooled connections
export DUCKDB_DATABASE_PATH="/tmp/terrafloww_duckdb.db"

# One-liner equivalent:
# export API_ATTACH_TABLE_PATH="s3://tf-datalake-bucket/deltalake-tables/unified_stac_table_v2" \
#   && export API_ATTACH_TABLE_ALIAS="unified_stac" \
#   && uv run uvicorn data_marketplace.api.main:app --host 127.0.0.1 --port 8099

# Start the API server
echo "Starting API server..."
uv run uvicorn data_marketplace.api.main:app --host 127.0.0.1 --port 8099
