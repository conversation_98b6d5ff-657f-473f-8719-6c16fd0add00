#!/bin/bash
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Test script to debug API route issues

BASE_URL="http://127.0.0.1:8099"

echo "🔍 Testing API Routes on VM"
echo "=========================="
echo "Base URL: $BASE_URL"
echo ""

echo "1. Testing root endpoint:"
curl -s -m 5 "$BASE_URL/" | head -c 100
echo ""
echo ""

echo "2. Testing /docs endpoint:"
curl -s -m 5 "$BASE_URL/docs" | head -c 100
echo ""
echo ""

echo "3. Testing /v1/datasets endpoint:"
curl -s -m 5 "$BASE_URL/v1/datasets"
echo ""
echo ""

echo "4. Testing /v1/scenes with minimal params (JSON debug):"
curl -s -m 30 "$BASE_URL/v1/scenes?dataset=sentinel-2-l2a&date_start=2025-01-01&date_end=2025-01-01&limit=1&fmt=json" \
  -w "\nHTTP Status: %{http_code}\nTime: %{time_total}s\n"
echo ""

echo "5. Testing /v1/scenes with Arrow accept header:"
curl -s -m 30 "$BASE_URL/v1/scenes?dataset=sentinel-2-l2a&date_start=2025-01-01&date_end=2025-01-01&limit=1" \
  -H "Accept: application/vnd.apache.arrow.stream" \
  -w "\nHTTP Status: %{http_code}\nTime: %{time_total}s\nSize: %{size_download} bytes\n" \
  -o /tmp/arrow_response.bin
echo ""

echo "6. Testing /v1/scenes:assets endpoint (JSON debug):"
curl -s -m 30 "$BASE_URL/v1/scenes:assets?dataset=sentinel-2-l2a&date_start=2025-01-01&date_end=2025-01-01&limit=1&fmt=json" \
  -w "\nHTTP Status: %{http_code}\nTime: %{time_total}s\n"
echo ""

echo "7. Testing /v1/scenes:assets with band codes (B02):"
curl -s -m 30 "$BASE_URL/v1/scenes:assets?dataset=sentinel-2-l2a&date_start=2025-01-14&date_end=2025-01-14&assets=B02&limit=2&fmt=json" \
  -w "\nHTTP Status: %{http_code}\nTime: %{time_total}s\n"
echo ""

echo "7. Testing invalid endpoint:"
curl -s -m 5 "$BASE_URL/invalid" \
  -w "\nHTTP Status: %{http_code}\n"
echo ""

echo "=========================="
echo "✅ Route testing complete!"
