#!/usr/bin/env python3
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Test script for the optimized Delta Lake table with 32MB row groups.

This script tests the new unified_stac_table_v2 with progressive complexity:
1. Basic connectivity test
2. Small date range query
3. Geometry-based spatial query
4. Asset-level query with COG metadata

The optimized table should perform much better due to:
- 32MB row groups (better for selective queries)
- Proper partitioning (year/month)
- S2 spatial indexing
- Optimized Parquet settings
"""

import asyncio
import io
import time
from datetime import datetime
from typing import Optional

import httpx
import pyarrow.ipc as ipc
from shapely.geometry import box
from shapely import wkb


def create_test_geometry() -> str:
    """Create a small test geometry (bbox around Bangalore) as hex WKB."""
    # Small bbox around Bangalore, India
    bbox_geom = box(77.5, 12.9, 77.7, 13.1)  # ~20km x 20km area
    return wkb.dumps(bbox_geom, hex=True)


async def test_basic_connectivity(base_url: str) -> bool:
    """Test basic API connectivity."""
    print("🔌 Testing basic API connectivity...")
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            resp = await client.get(f"{base_url}/v1/datasets")
            if resp.status_code == 200:
                print("✅ API is responding")
                return True
            else:
                print(f"❌ API returned status {resp.status_code}")
                return False
    except Exception as e:
        print(f"❌ API connectivity failed: {e}")
        return False


async def test_small_date_query(base_url: str, timeout: float = 60.0) -> Optional[int]:
    """Test with a very small date range (2 days) and limit=1."""
    print("📅 Testing small date range query (2 days, limit=1)...")
    
    params = {
        "dataset": "sentinel-2-l2a",
        "date_start": "2025-01-01",
        "date_end": "2025-01-02",  # Just 2 days
        "limit": 1,  # Just 1 row
    }
    
    headers = {"Accept": "application/vnd.apache.arrow.stream"}
    
    try:
        start_time = time.time()
        async with httpx.AsyncClient(timeout=timeout) as client:
            resp = await client.get(f"{base_url}/v1/scenes", params=params, headers=headers)
            
            if resp.status_code >= 400:
                error_text = resp.text
                print(f"❌ Query failed with status {resp.status_code}: {error_text}")
                return None

            print(f"   Response received, parsing Arrow data...")
            
            # Parse Arrow response
            buf = io.BytesIO(resp.content)
            reader = ipc.open_stream(buf)
            table = reader.read_all()
            
            elapsed = time.time() - start_time
            print(f"✅ Small date query successful: {len(table)} rows in {elapsed:.2f}s")
            
            if len(table) > 0:
                print(f"   Sample scene_id: {table['scene_id'][0].as_py()}")
                print(f"   Sample datetime: {table['datetime'][0].as_py()}")
            
            return len(table)
            
    except Exception as e:
        print(f"❌ Small date query failed: {e}")
        return None


async def test_geometry_query(base_url: str, timeout: float = 120.0) -> Optional[int]:
    """Test with geometry filtering (should be much faster with S2 indexing)."""
    print("🌍 Testing geometry-based spatial query...")
    
    geometry_wkb = create_test_geometry()
    
    params = {
        "dataset": "sentinel-2-l2a",
        "date_start": "2024-12-01",
        "date_end": "2024-12-31",  # 1 month
        "geometry_wkb": geometry_wkb,
        "limit": 5,  # Small limit
    }
    
    headers = {"Accept": "application/vnd.apache.arrow.stream"}
    
    try:
        start_time = time.time()
        async with httpx.AsyncClient(timeout=timeout) as client:
            resp = await client.get(f"{base_url}/v1/scenes", params=params, headers=headers)
            
            if resp.status_code >= 400:
                error_text = resp.text
                print(f"❌ Geometry query failed with status {resp.status_code}: {error_text}")
                return None
            
            # Parse Arrow response
            buf = io.BytesIO(resp.content)
            reader = ipc.open_stream(buf)
            table = reader.read_all()
            
            elapsed = time.time() - start_time
            print(f"✅ Geometry query successful: {len(table)} rows in {elapsed:.2f}s")
            
            if len(table) > 0:
                print(f"   Found scenes in Bangalore area")
                print(f"   Sample scene_id: {table['scene_id'][0].as_py()}")
            
            return len(table)
            
    except Exception as e:
        print(f"❌ Geometry query failed: {e}")
        return None


async def test_assets_query(base_url: str, timeout: float = 180.0) -> Optional[int]:
    """Test the assets endpoint with COG metadata."""
    print("🖼️  Testing assets query with COG metadata...")
    
    geometry_wkb = create_test_geometry()
    
    params = {
        "dataset": "sentinel-2-l2a",
        "date_start": "2024-12-15",
        "date_end": "2024-12-16",  # Just 1 day
        "geometry_wkb": geometry_wkb,
        "assets": "red,nir08",  # Specific assets
        "limit": 3,
    }
    
    headers = {"Accept": "application/vnd.apache.arrow.stream"}
    
    try:
        start_time = time.time()
        async with httpx.AsyncClient(timeout=timeout) as client:
            resp = await client.get(f"{base_url}/v1/scenes:assets", params=params, headers=headers)
            
            if resp.status_code >= 400:
                error_text = resp.text
                print(f"❌ Assets query failed with status {resp.status_code}: {error_text}")
                return None
            
            # Parse Arrow response
            buf = io.BytesIO(resp.content)
            reader = ipc.open_stream(buf)
            table = reader.read_all()
            
            elapsed = time.time() - start_time
            print(f"✅ Assets query successful: {len(table)} rows in {elapsed:.2f}s")
            
            if len(table) > 0:
                print(f"   Sample cog_key: {table['cog_key'][0].as_py()}")
                print(f"   Sample cog_width: {table['cog_width'][0].as_py()}")
                print(f"   Sample cog_height: {table['cog_height'][0].as_py()}")
            
            return len(table)
            
    except Exception as e:
        print(f"❌ Assets query failed: {e}")
        return None


async def main():
    """Run all tests progressively."""
    base_url = "http://127.0.0.1:8099"
    
    print("🚀 Testing Optimized Delta Lake Table (unified_stac_table_v2)")
    print("=" * 60)
    print(f"API Base URL: {base_url}")
    print(f"Expected table: unified_stac_table_v2 (32MB row groups)")
    print()
    
    # Test 1: Basic connectivity
    if not await test_basic_connectivity(base_url):
        print("❌ Cannot proceed - API is not responding")
        return
    
    print()
    
    # Test 2: Small date range
    small_result = await test_small_date_query(base_url)
    if small_result is None:
        print("❌ Cannot proceed - basic queries are failing")
        return
    
    print()
    
    # Test 3: Geometry-based query (should be fast with S2 indexing)
    geo_result = await test_geometry_query(base_url)
    if geo_result is None:
        print("⚠️  Geometry queries are failing, but continuing...")
    
    print()
    
    # Test 4: Assets query with COG metadata
    assets_result = await test_assets_query(base_url)
    if assets_result is None:
        print("⚠️  Assets queries are failing")
    
    print()
    print("=" * 60)
    print("🎯 Test Summary:")
    print(f"   Basic connectivity: ✅")
    print(f"   Small date query: {'✅' if small_result is not None else '❌'} ({small_result} rows)")
    print(f"   Geometry query: {'✅' if geo_result is not None else '❌'} ({geo_result} rows)")
    print(f"   Assets query: {'✅' if assets_result is not None else '❌'} ({assets_result} rows)")
    
    if all(r is not None for r in [small_result, geo_result, assets_result]):
        print("🎉 All tests passed! The optimized table is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the logs above for details.")


if __name__ == "__main__":
    asyncio.run(main())
