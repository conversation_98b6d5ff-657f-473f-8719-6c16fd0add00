# SPDX-FileCopyrightText: Terrafloww Labs, 2025

import pytest

from data_marketplace.utils.band_alias import normalize_assets


@pytest.mark.parametrize(
    "dataset,assets,expected",
    [
        ("sentinel-2-l2a", "B02", "blue"),
        ("sentinel-2-l2a", "B8A", "nir08"),
        ("sentinel-2-l2a", "B11", "swir16"),
        ("sentinel-2-l2a", "SCL", "scl"),
        ("sentinel-2-l2a", "B02,B03,B04", "blue,green,red"),
        ("sentinel-2-l2a", "blue,red", "blue,red"),
        ("landsat-c2l2-sr", "B2", "blue"),
        ("landsat-c2l2-sr", "B7", "swir22"),
        ("landsat-c2l2-sr", "B2,B5,B6", "blue,nir08,swir16"),
    ],
)
def test_normalize_assets_basic(dataset: str, assets: str, expected: str) -> None:
    assert normalize_assets(dataset, assets) == expected


def test_normalize_assets_mixed_inputs() -> None:
    # Mixed codes + canonical should map codes and keep canonical as-is
    out = normalize_assets("sentinel-2-l2a", "B02,red,nir08,B11")
    assert out == "blue,red,nir08,swir16"


def test_normalize_assets_unknown_dataset() -> None:
    # Unknown dataset: passthrough (no mapping)
    out = normalize_assets("unknown", "B02,blue")
    assert out == "B02,blue"

