# Tests README

## Start the API (VM / local)

Use the provided start script which sets the required environment variables:

```bash
bash tests/start_api_optimized.sh
```

Equivalent one-liner:

```bash
export API_ATTACH_TABLE_PATH="s3://tf-datalake-bucket/deltalake-tables/unified_stac_table_v2" \
  && export API_ATTACH_TABLE_ALIAS="unified_stac" \
  && uv run uvicorn data_marketplace.api.main:app --host 127.0.0.1 --port 8099
```

The server should start on http://127.0.0.1:8099.

## Smoke test the API routes

Once the server is running, execute:

```bash
bash tests/test_api_routes.sh
```

This includes JSON debug calls (&fmt=json) and a band code mapping check
(/v1/scenes:assets with assets=B02).

## Run unit tests

Run unit tests (they do not start the server):

```bash
uv run pytest -q
```

Included:
- test_band_alias.py: tests server-side normalize_assets behavior

## Notes

- Route tests assume the server is already running (they do not start/stop it).
- Arrow is the default transport; for quick inspection, some calls use fmt=json.

