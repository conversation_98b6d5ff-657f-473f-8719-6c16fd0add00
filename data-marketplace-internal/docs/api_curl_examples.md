// SPDX-FileCopyrightText: Terrafloww Labs, 2025

# API curl examples (copy/paste-safe)

Notes
- Use plain ASCII `&` in query strings (do not use `&amp;`).
- Public endpoints default to Apache Arrow IPC (binary). Add `&fmt=json` for JSON.
- Date-only inputs are treated as full-day ranges [startT00Z, nextDayT00Z).

## 1) Health
```bash
curl -s "http://127.0.0.1:8099/health" | jq .
```

## 2) Attached table info
```bash
curl -i "http://127.0.0.1:8099/tables/unified_stac"
```

## 3) /q: min/max datetime (JSON)
```bash
curl -s -X POST "http://127.0.0.1:8099/q?fmt=json" \
  -H "Content-Type: application/json" \
  -d '{"sql":"SELECT MIN(datetime) AS min_dt, MAX(datetime) AS max_dt FROM unified_stac"}' | jq .
```

## 4) /q: count in a day range (parameters)
```bash
curl -s -X POST "http://127.0.0.1:8099/q?fmt=json" \
  -H "Content-Type: application/json" \
  -d '{"sql":"SELECT COUNT(*) AS c FROM unified_stac WHERE datetime >= {s} AND datetime < {e}","parameters":{"s":"2025-01-14T00:00:00Z","e":"2025-01-15T00:00:00Z"}}' | jq .
```

## 5) /v1/scenes: Arrow (headers show metrics)
```bash
curl -D - "http://127.0.0.1:8099/v1/scenes?dataset=sentinel-2-l2a&date_start=2025-01-14&date_end=2025-01-14&limit=1" -o /dev/null -s
```

## 6) /v1/scenes: JSON (debug)
```bash
curl -s "http://127.0.0.1:8099/v1/scenes?dataset=sentinel-2-l2a&date_start=2025-01-14&date_end=2025-01-14&limit=1&fmt=json" | jq .
```

## 7) /v1/scenes: full-day ISO range (equivalent)
```bash
curl -s "http://127.0.0.1:8099/v1/scenes?dataset=sentinel-2-l2a&date_start=2025-01-14T00:00:00Z&date_end=2025-01-15T00:00:00Z&limit=1&fmt=json" | jq .
```

## 8) /v1/scenes:assets: JSON
```bash
curl -s "http://127.0.0.1:8099/v1/scenes:assets?dataset=sentinel-2-l2a&date_start=2025-01-14&date_end=2025-01-14&limit=1&fmt=json" | jq .
```

## 9) /v1/scenes with geometry_wkb (replace GEOM_WKB_HEX)
```bash
curl -D - "http://127.0.0.1:8099/v1/scenes?dataset=sentinel-2-l2a&date_start=2025-01-14&date_end=2025-01-14&geometry_wkb=GEOM_WKB_HEX&limit=1" -o /dev/null -s
```

## 10) /v1/scenes:assets with asset key filter (optional)
```bash
curl -s "http://127.0.0.1:8099/v1/scenes:assets?dataset=sentinel-2-l2a&date_start=2025-01-14&date_end=2025-01-14&assets=B02&limit=1&fmt=json" | jq .
```

## 11) Read Arrow stream file (from 5 or 10)
```python
import pyarrow as pa
import pyarrow.ipc as ipc

file_path = '/tmp/assets.arrow'

# Public endpoints return Arrow IPC STREAMS, not FILE format
with pa.memory_map(file_path, 'r') as source:
    reader = ipc.open_stream(source)  # use open_stream for Arrow stream
    table = reader.read_all()

print(table)
```