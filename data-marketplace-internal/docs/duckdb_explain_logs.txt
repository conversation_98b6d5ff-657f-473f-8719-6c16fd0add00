[EXPLAIN ANALYZE]
explain_key: analyzed_plan

Query Profiling Information

EXPLAIN ANALYZE SELECT scene_id, collection, datetime, bbox, cog_key, cog_href, cog_href AS request_url, cog_width, cog_height, cog_tile_width, cog_tile_height, cog_crs_code, cog_transform, cog_tile_offsets, cog_tile_byte_counts, cog_dtype_code, cog_bits_per_sample, cog_compression_code, cog_predictor, cog_dn_scale, cog_dn_offset, cog_roles
FROM delta_scan('s3://tf-datalake-bucket/deltalake-tables/unified_stac_table_v2')
WHERE datetime >= '2025-01-10T00:00:00Z'
     AND datetime < '2025-02-15T00:00:00Z'
     AND year = 2025
     AND month IN (1,2)
     AND cog_key IN ('B02','B03','B04','blue','green','red')
     AND bbox.xmin <= 77.58
     AND bbox.ymin <= 13.08
     AND bbox.xmax >= 77.55
     AND bbox.ymax >= 13.01
     AND (s2_cell_id IN ('4300656169162113024') OR (list_contains(s2_cells, '4300656169162113024')))
ORDER BY datetime DESC
LIMIT 8 OFFSET 0

Total Time: 1.67s

QUERY
     EXPLAIN_ANALYZE
          0 Rows (0.00s)

     TOP_N
          Top: 8
          Order By: delta_scan.datetime DESC
          8 Rows (0.00s)

     PROJECTION
          scene_id, collection, datetime, bbox, cog_key, cog_href, request_url, cog_width, cog_height, cog_tile_width, cog_tile_height, cog_crs_code, cog_transform, cog_tile_offsets, cog_tile_byte_counts, cog_dtype_code, cog_bits_per_sample, cog_compression_code, cog_predictor, cog_dn_scale, cog_dn_offset, cog_roles
          18 Rows (0.00s)

     PROJECTION
          #0, #1, #2, #5, #6, #7, #8, #9, #10, #11, #12, #13, #14, #15, #16, #17, #18, #19, #20, #21, #22
          18 Rows (0.00s)

     FILTER
          (IN (...) AND ((s2_cell_id = '4300656169162113024') OR list_contains(s2_cells, '4300656169162113024')))
          18 Rows (0.00s)

     HASH_JOIN
          Join Type: MARK
          Conditions: cog_key = #0
          96 Rows (0.00s)

          TABLE_SCAN
               Projections: datetime, cog_key, bbox, s2_cell_id, s2_cells, scene_id, collection, cog_href, cog_width, cog_height, cog_tile_width, cog_tile_height, cog_crs_code, cog_transform, cog_tile_offsets, cog_tile_byte_counts, cog_dtype_code, cog_bits_per_sample, cog_compression_code, cog_predictor, cog_dn_scale, cog_dn_offset, cog_roles
               Filters:
                    datetime >= '2025-01-10 00:00:00+00'::TIMESTAMP WITH TIME ZONE
                    AND datetime < '2025-02-15 00:00:00+00'::TIMESTAMP WITH TIME ZONE
                    AND optional: Dynamic Filter (datetime)
                    year = 2025
                    month >= 1 AND month <= 2
                    optional: cog_key IN ('B02', 'B03', 'B04', 'blue', 'green', 'red')
                    bbox.xmin <= 77.58 AND bbox.ymin <= 13.08 AND bbox.xmax >= 77.55 AND bbox.ymax >= 13.01

               File Filters:
                    datetime >= '2025-01-10 00:00:00+00'::TIMESTAMP WITH TIME ZONE
                    AND datetime < '2025-02-15 00:00:00+00'::TIMESTAMP WITH TIME ZONE
                    bbox.ymax >= 13.01 AND bbox.xmax >= 77.55 AND bbox.ymin <= 13.08 AND bbox.xmin <= 77.58
                    optional: cog_key IN ('B02', 'B03', 'B04', 'blue', 'green', 'red')
                    year = 2025
                    month >= 1 AND month <= 2

               Scanning Files: 52/52
               Total Files Read: 52
               96 Rows (2.21s)

          COLUMN_DATA_SCAN
               6 Rows (0.00s)

---

[EXPLAIN ANALYZE]
explain_key: analyzed_plan

Query Profiling Information

EXPLAIN ANALYZE SELECT scene_id, collection, datetime, bbox, cog_key, cog_href, cog_href AS request_url, cog_width, cog_height, cog_tile_width, cog_tile_height, cog_crs_code, cog_transform, cog_tile_offsets, cog_tile_byte_counts, cog_dtype_code, cog_bits_per_sample, cog_compression_code, cog_predictor, cog_dn_scale, cog_dn_offset, cog_roles
FROM delta_scan('s3://tf-datalake-bucket/deltalake-tables/unified_stac_table_v2')
WHERE datetime >= '2025-01-10T00:00:00Z'
     AND datetime < '2025-02-15T00:00:00Z'
     AND year = 2025
     AND month IN (1,2)
     AND cog_key IN ('B02','B03','B04','blue','green','red')
     AND bbox.xmin <= 77.58
     AND bbox.ymin <= 13.08
     AND bbox.xmax >= 77.55
     AND bbox.ymax >= 13.01
ORDER BY datetime DESC
LIMIT 8 OFFSET 0

Total Time: 1.60s

QUERY
     EXPLAIN_ANALYZE
          0 Rows (0.00s)

     TOP_N
          Top: 8
          Order By: delta_scan.datetime DESC
          8 Rows (0.00s)

     PROJECTION
          scene_id, collection, datetime, bbox, cog_key, cog_href, request_url, cog_width, cog_height, cog_tile_width, cog_tile_height, cog_crs_code, cog_transform, cog_tile_offsets, cog_tile_byte_counts, cog_dtype_code, cog_bits_per_sample, cog_compression_code, cog_predictor, cog_dn_scale, cog_dn_offset, cog_roles
          18 Rows (0.00s)

     PROJECTION
          #0, #1, #2, #3, #4, #5, #6, #7, #8, #9, #10, #11, #12, #13, #14, #15, #16, #17, #18, #19, #20
          18 Rows (0.00s)

     FILTER
          IN (...)
          18 Rows (0.00s)

     HASH_JOIN
          Join Type: MARK
          Conditions: cog_key = #0
          96 Rows (0.00s)

          TABLE_SCAN
               Projections: datetime, cog_key, bbox, scene_id, collection, cog_href, cog_width, cog_height, cog_tile_width, cog_tile_height, cog_crs_code, cog_transform, cog_tile_offsets, cog_tile_byte_counts, cog_dtype_code, cog_bits_per_sample, cog_compression_code, cog_predictor, cog_dn_scale, cog_dn_offset, cog_roles
               Filters:
                    datetime >= '2025-01-10 00:00:00+00'::TIMESTAMP WITH TIME ZONE
                    AND datetime < '2025-02-15 00:00:00+00'::TIMESTAMP WITH TIME ZONE
                    AND optional: Dynamic Filter (datetime)
                    year = 2025
                    month >= 1 AND month <= 2
                    optional: cog_key IN ('B02', 'B03', 'B04', 'blue', 'green', 'red')
                    bbox.xmin <= 77.58 AND bbox.ymin <= 13.08 AND bbox.xmax >= 77.55 AND bbox.ymax >= 13.01

               File Filters:
                    datetime >= '2025-01-10 00:00:00+00'::TIMESTAMP WITH TIME ZONE
                    AND datetime < '2025-02-15 00:00:00+00'::TIMESTAMP WITH TIME ZONE
                    bbox.ymax >= 13.01 AND bbox.xmax >= 77.55 AND bbox.ymin <= 13.08 AND bbox.xmin <= 77.58
                    optional: cog_key IN ('B02', 'B03', 'B04', 'blue', 'green', 'red')
                    year = 2025
                    month >= 1 AND month <= 2

               Scanning Files: 52/52
               Total Files Read: 52
               96 Rows (2.18s)

          COLUMN_DATA_SCAN
               6 Rows (0.00s)

---

[EXPLAIN ANALYZE]
explain_key: analyzed_plan

Query Profiling Information

EXPLAIN ANALYZE SELECT scene_id, collection, datetime, bbox, cog_key, cog_href, cog_href AS request_url, cog_width, cog_height, cog_tile_width, cog_tile_height, cog_crs_code, cog_transform, cog_tile_offsets, cog_tile_byte_counts, cog_dtype_code, cog_bits_per_sample, cog_compression_code, cog_predictor, cog_dn_scale, cog_dn_offset, cog_roles
FROM delta_scan('s3://tf-datalake-bucket/deltalake-tables/unified_stac_table_v2')
WHERE datetime >= '2025-01-20T00:00:00Z'
     AND datetime < '2025-02-05T00:00:00Z'
     AND year = 2025
     AND month IN (1,2)
     AND cog_key IN ('B02','B03','B04','blue','green','red')
     AND bbox.xmin <= 77.58
     AND bbox.ymin <= 13.08
     AND bbox.xmax >= 77.55
     AND bbox.ymax >= 13.01
ORDER BY datetime DESC
LIMIT 8 OFFSET 0

HTTPFS HTTP Stats
     in: 0 bytes
     out: 0 bytes
     #HEAD: 0
     #GET: 0
     #PUT: 0
     #POST: 0
     #DELETE: 0

Total Time: 1.22s

QUERY
     EXPLAIN_ANALYZE
          0 Rows (0.00s)

     TOP_N
          Top: 8
          Order By: delta_scan.datetime DESC
          8 Rows (0.00s)

     PROJECTION
          scene_id, collection, datetime, bbox, cog_key, cog_href, request_url, cog_width, cog_height, cog_tile_width, cog_tile_height, cog_crs_code, cog_transform, cog_tile_offsets, cog_tile_byte_counts, cog_dtype_code, cog_bits_per_sample, cog_compression_code, cog_predictor, cog_dn_scale, cog_dn_offset, cog_roles
          9 Rows (0.00s)

     PROJECTION
          #0, #1, #2, #3, #4, #5, #6, #7, #8, #9, #10, #11, #12, #13, #14, #15, #16, #17, #18, #19, #20
          9 Rows (0.00s)

     FILTER
          IN (...)
          9 Rows (0.00s)

     HASH_JOIN
          Join Type: MARK
          Conditions: cog_key = #0
          48 Rows (0.00s)

          TABLE_SCAN
               Projections: datetime, cog_key, bbox, scene_id, collection, cog_href, cog_width, cog_height, cog_tile_width, cog_tile_height, cog_crs_code, cog_transform, cog_tile_offsets, cog_tile_byte_counts, cog_dtype_code, cog_bits_per_sample, cog_compression_code, cog_predictor, cog_dn_scale, cog_dn_offset, cog_roles
               Filters:
                    datetime >= '2025-01-20 00:00:00+00'::TIMESTAMP WITH TIME ZONE
                    AND datetime < '2025-02-05 00:00:00+00'::TIMESTAMP WITH TIME ZONE
                    AND optional: Dynamic Filter (datetime)
                    year = 2025
                    month >= 1 AND month <= 2
                    optional: cog_key IN ('B02', 'B03', 'B04', 'blue', 'green', 'red')
                    bbox.xmin <= 77.58 AND bbox.ymin <= 13.08 AND bbox.xmax >= 77.55 AND bbox.ymax >= 13.01

               File Filters:
                    datetime >= '2025-01-20 00:00:00+00'::TIMESTAMP WITH TIME ZONE
                    AND datetime < '2025-02-05 00:00:00+00'::TIMESTAMP WITH TIME ZONE
                    bbox.ymax >= 13.01 AND bbox.xmax >= 77.55 AND bbox.ymin <= 13.08 AND bbox.xmin <= 77.58
                    optional: cog_key IN ('B02', 'B03', 'B04', 'blue', 'green', 'red')
                    year = 2025
                    month >= 1 AND month <= 2

               Scanning Files: 52/52
               Total Files Read: 52
               48 Rows (1.12s)

          COLUMN_DATA_SCAN
               6 Rows (0.00s)