// SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Public Arrow API: Datasets, Scenes, and Scene Assets (Design)

This document defines the user-facing API that returns Apache Arrow IPC streams for discovery (scenes) and per-asset metadata (scene assets) needed by rasteret to compute HTTP ranges and read COG tiles. Internals (Delta, indexing, caching) are hidden behind a simple logical dataset interface.

## Goals

- Simple, opinionated public surface: datasets → scenes → scene assets
- Transport: Apache Arrow IPC over HTTP (content-type: application/vnd.apache.arrow.stream)
- Ergonomics: provide a thin rasteret client so most users never deal with Arrow mechanics
- Stability: schemas are versioned and kept stable; internal Delta layout is hidden

Initial dataset: `sentinel-2-l2a`

## Transport choice

- Media type: `application/vnd.apache.arrow.stream`
- Clients MUST set: `Accept: application/vnd.apache.arrow.stream`
- Server streams Arrow RecordBatches (chunked) to avoid buffering large results
- Error handling: on non-2xx status, return JSON error; clients only parse Arrow on 2xx
- Phase 2: consider Arrow Flight for very high-throughput clients after HTTP IPC stabilizes

## Public endpoints

### 1) List datasets

- `GET /v1/datasets`
- Returns JSON discovery (small):
  - name (string): dataset key, e.g., `sentinel-2-l2a`
  - description (string)
  - filters (object): supported filter knobs

Example JSON response:
```json
{
  "datasets": [
    {
      "name": "sentinel-2-l2a",
      "description": "Sentinel-2 L2A surface reflectance scenes and assets",
      "filters": {
        "date_start": "YYYY-MM-DD",
        "date_end": "YYYY-MM-DD",
        "bbox": "minx,miny,maxx,maxy (WGS84)",
        "cloud_cover_max": "float (optional)",
        "assets": "comma-separated asset keys (optional)",
        "limit": "int (default cap)",
        "offset": "int"
      }
    }
  ]
}
```

### 2) Scenes (discovery)

- `GET /v1/scenes`
- Query params (all strings unless noted):
  - `dataset` (required), e.g., `sentinel-2-l2a`
  - `date_start`, `date_end` (required): ISO date strings; aligned to STAC datetime semantics
  - `bbox` (optional): `minx,miny,maxx,maxy` (WGS84)
  - `cloud_cover_max` (optional): float
  - `limit` (optional): int, default server cap
  - `offset` (optional): int
- Response: Arrow IPC stream with the Scenes schema (below)


Notes:
- Date-only inputs like `YYYY-MM-DD` are treated as full-day ranges: `[startT00:00:00Z, nextDayT00:00:00Z)`
- Inputs with times are honored exactly
- Server applies partition pruning (year/month) internally for performance
- For debugging, you can add `fmt=json` to get a small JSON response (Arrow remains default)

### 3) Scene assets (per-asset metadata)

- `GET /v1/scenes:assets`
- Query params: same as `/v1/scenes` plus `assets` (optional), e.g., `red,nir08`
- Response: Arrow IPC stream with the Scene Assets schema (below)

## Schemas (stable)

Assets filter:
- Accepts canonical asset names (e.g., `blue,red,nir,swir16,rededge1`)
- Also accepts band codes for convenience (dataset-aware):
  - Sentinel‑2: `B01→coastal, B02→blue, B03→green, B04→red, B05→rededge1, B06→rededge2, B07→rededge3, B08→nir, B8A→nir08, B09→nir09, B11→swir16, B12→swir22, SCL→scl`
  - Landsat 9: `B1→coastal, B2→blue, B3→green, B4→red, B5→nir08, B6→swir16, B7→swir22`
- Multiple assets can be provided as comma-separated list
- Mapping logic is implemented server-side via `data_marketplace.utils.band_alias.normalize_assets`


We keep schemas stable and versioned (via added endpoint `/v1/scenes:schema` and `/v1/scenes:assets:schema` returning JSON for SDKs). The Arrow IPC carries the authoritative schema.

### Scenes schema (discovery)

- `item_id`: utf8
- `collection`: utf8
- `datetime`: timestamp[us, tz="UTC"]
- `bbox_xyxy`: fixed_size_list<float64, 4>  // minx, miny, maxx, maxy
- `scene_cloud_cover`: float32 (nullable)
- `dataset`: utf8  // echo of requested dataset

Notes:
- datetime aligns with STAC datetime; server coerces to UTC
- bbox storage in Delta may differ; we expose a fixed-size list for efficient client parsing; pushdown is handled internally via S2 or bbox

### Scene Assets schema (per-asset)

Identity:
- `item_id`: utf8
- `collection`: utf8
- `datetime`: timestamp[us, tz="UTC"]
- `bbox_xyxy`: fixed_size_list<float64, 4>
- `asset_key`: utf8
- `cog_href`: utf8  // original href (may be s3://)
- `request_url`: utf8  // normalized http(s) URL for Range requests
- `dataset`: utf8

Core fields for rasteret:
- `width`: int32
- `height`: int32
- `tile_width`: int32
- `tile_height`: int32
- `crs`: int32  // EPSG code
- `transform`: fixed_size_list<float64, 4>  // [scale_x, translate_x, scale_y, translate_y]
- `tile_offsets`: list<int64>
- `tile_byte_counts`: list<int64>

Types/encoding (machine + human friendly):
- `dtype_numpy`: utf8  // e.g., "uint16"
- `dtype_arrow`: utf8 (nullable)  // optional Arrow type string
- `compression_code`: int16 (nullable)
- `compression_name`: utf8 (nullable)
- `predictor_code`: int16 (nullable)
- `predictor_name`: utf8 (nullable)

Optional extras:
- `pixel_scale`: list<float64> (nullable)
- `tiepoint`: list<float64> (nullable)
- `cog_dn_scale`: float64 (nullable)
- `cog_dn_offset`: float64 (nullable)
- `roles`: list<utf8> (nullable)
- `media_type`: utf8 (nullable)
- `title`: utf8 (nullable)
- `description`: utf8 (nullable)

Rationale:
- Matches rasteret.types.CogMetadata and fetch logic without translation
- Includes human-readable fields using TIFF mappings for better UX

## Dataset registry and internal hiding

- We expose logical datasets (e.g., `sentinel-2-l2a`) while internally querying a unified Delta table or multiple tables.
- A dataset registry maps public `dataset` to:
  - Internal table alias or view
  - Column adapters for: item_id, collection, datetime, bbox, assets
  - Optional default filters or transforms
- Spatial pushdown: we can use either bbox predicates or S2 cell predicates (preferred for pruning); both are internal details. The public API keeps only `bbox` filter for simplicity.

## Filter → SQL (internal)

- Build WHERE clauses based on filters:
  - Date: `datetime BETWEEN ? AND ?`
  - BBox (if used): geometry overlaps or bbox intersects
  - S2 pushdown (preferred when available): s2_cell_id IN (...) or s2_cells overlaps
  - Cloud cover: `cloud_cover <= ?` when present
  - Assets: asset key filtering if assets are exploded; otherwise post-filter before parsing
- Use DuckDBService with attached Delta tables and PIN_SNAPSHOT
- Keep table aliasing/config in settings; do not expose paths

## Arrow streaming implementation

- For `/v1/scenes`:
  - Execute query via DuckDBService
  - Convert to public scenes schema (column rename/reshape as needed)
  - Stream Arrow RecordBatches via IPC writer (chunked transfer)

- For `/v1/scenes:assets`:
  - Resolve scenes as above
  - For each scene (batched):
    - Identify assets (filter by keys if provided)
    - Normalize href → request_url (http/https)
    - READ COG FIELDS FROM DELTA (no header parsing at request time): map `cog_*` columns to public schema fields
    - Enrich with TIFF mappings (compression/predictor names) using local lookup tables
  - Stream Arrow RecordBatches as they’re ready; do not buffer full result set

- Response headers:
  - `Content-Type: application/vnd.apache.arrow.stream`
  - `X-Query-Time-Ms`, `X-Rows-Returned` (approx), `X-Bytes-Scanned` (if available)
  - `X-Dataset` (echo)

## Rasteret client helpers (ergonomics)

Provide a small helper module (in rasteret or a tiny SDK):

- `list_scenes(base_url, dataset, date_range, bbox=None, cloud_cover_max=None, limit=1000, offset=0, headers=None) -> pa.Table`
  - Issues GET /v1/scenes, parses Arrow via `pyarrow.ipc.open_stream`

- `iter_scene_assets(base_url, dataset, filters..., assets=None, headers=None) -> Iterator[pa.RecordBatch]`
  - Issues GET /v1/scenes:assets, yields batches

- `to_cog_metadata(record) -> rasteret.types.CogMetadata`
  - Maps one row to CogMetadata (transform list, crs int, dtype, offsets/byte_counts)

- `read_tiles_from_api(record, geometry=None, max_concurrent=150) -> (np.ndarray, Affine|None)`
  - Convenience wrapper around rasteret.fetch.cog.read_cog_tile_data

Advanced users: document raw pyarrow usage; JS users can use Apache Arrow JS to read the stream.

## Schema discovery endpoints (JSON)

- `GET /v1/scenes:schema` and `GET /v1/scenes:assets:schema`
  - Return JSON describing fields and types for SDKs and advanced users
  - Useful for tooling and tests; Arrow IPC remains the authoritative transport

## Data types and standards

- `datetime`: Arrow `timestamp[us, tz="UTC"]`, aligned to STAC datetime semantics
- `bbox`: public field is `bbox_xyxy` fixed-size list of 4 float64
  - Internally, pushdown may use S2 cell predicates for pruning; bbox or GeoArrow predicates can also be employed depending on table statistics
- Compression/predictor: include both codes and names using TIFF mappings
- `cog_dn_scale` / `cog_dn_offset`: nullable floats; only set if present in headers or STAC metadata

## Limits, caps, and safety

- Default `limit`: e.g., 10,000 rows per call (configurable); still streamed in batches
- Timeouts and backpressure: enforced by DuckDBService and endpoint logic
- Per-request caps on assets to avoid pathological queries

## Open items and decisions

- Confirm Delta schema adapters for `sentinel-2-l2a` (item_id, datetime, bbox, assets)
- Validate bbox vs S2 pushdown strategy per dataset based on table stats and bloom filters
- Finalize JSON for schema discovery endpoints (fields and types naming)

---

This design keeps the public surface simple and ergonomic while leveraging Arrow IPC for efficiency and typed data interchange. It aligns directly with rasteret’s query-side expectations so users can go from API → CogMetadata → tile reads with minimal glue.

