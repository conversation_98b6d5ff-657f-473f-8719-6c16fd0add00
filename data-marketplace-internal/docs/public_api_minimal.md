// SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Terrafloww Data API (Minimal Public Docs)

This API returns Apache Arrow IPC streams for simple dataset queries. Internals are abstracted; users only need to know datasets, filters, and how to consume Arrow.

## Endpoints

- GET /v1/datasets (JSON)
  - List available datasets and supported filters.
- GET /v1/scenes (Arrow IPC)
  - Query scenes with filters and get discovery rows.
- GET /v1/scenes:assets (Arrow IPC)
  - Query per-asset rows needed to read COGs with rasteret.

## Filters (query params)

- dataset: string (e.g., sentinel-2-l2a)
- date_start, date_end: ISO dates
- bbox: minx,miny,maxx,maxy (optional)
- geometry_wkb: hex-encoded WKB geometry (optional)
- cloud_cover_max: float (optional)
- assets: comma-separated asset keys (for /v1/scenes:assets)
- limit, offset: ints (server caps apply)

Notes
- If geometry_wkb is given, the server applies spatial pruning internally (S2-based).
- You receive Arrow IPC; parse with Apache Arrow in your client.

## Example usage (Python + pyarrow)

```python
import requests
import pyarrow as pa
import pyarrow.ipc as ipc

base = "https://api.terrafloww.example"
params = {
    "dataset": "sentinel-2-l2a",
    "date_start": "2025-08-01",
    "date_end": "2025-08-31",
    # optional: "geometry_wkb": "...",
}

r = requests.get(f"{base}/v1/scenes", params=params, headers={"Accept": "application/vnd.apache.arrow.stream"}, stream=True)
r.raise_for_status()
reader = ipc.open_stream(r.raw)
table = reader.read_all()
print(table.schema)
```

## Rasteret helper sketch

Use the API outputs to build rasteret CogMetadata and read tiles.

```python
from rasteret.fetch.cog import read_cog_tile_data
from rasteret.types import CogMetadata

# record is a dict/row from /v1/scenes:assets Arrow table
# Convert 6-element cog_transform to rasteret transform_4
# [scale_x, translate_x, scale_y, translate_y]
cog_transform = record["cog_transform"]
transform_4 = [cog_transform[1], cog_transform[0], cog_transform[5], cog_transform[3]]

meta = CogMetadata(
    width=record["cog_width"], height=record["cog_height"],
    tile_width=record["cog_tile_width"], tile_height=record["cog_tile_height"],
    dtype="uint16",  # derive from cog_dtype_code + cog_bits_per_sample
    crs=record["cog_crs_code"],
    predictor=record["cog_predictor"],
    compression=record["cog_compression_code"],
    transform=transform_4,
    tile_offsets=record["cog_tile_offsets"],
    tile_byte_counts=record["cog_tile_byte_counts"],
)

url = record["request_url"]
data, affine = await read_cog_tile_data(url, meta, geometry=None)
```

## Media type

- Use Accept: application/vnd.apache.arrow.stream
- Responses on success are Arrow IPC streams; errors are JSON with HTTP codes.

