{"timestamp": "2025-08-23T23:58:24.786817", "test_geometry_bounds": [77.55, 13.01, 77.58, 13.08], "delta_statistics": {"total_files": 6499620, "min_datetime": "2025-01-01 00:04:41.476000+00:00", "max_datetime": "2025-02-09 23:51:52.918000+00:00", "unique_years": 1, "unique_months": 2, "unique_s2_cells": 11975, "min_lon": -179.982555, "max_lon": 179.998017, "min_lat": -82.846582, "max_lat": 70.879577}, "s2_distribution": {"distribution": [{"s2_cell_id": "13462666661093965824", "record_count": 13856, "min_date": "2025-01-01 07:16:41.074000+00:00", "max_date": "2025-02-09 12:45:28.545000+00:00", "min_lon": -93.001106, "max_lon": -80.431927, "min_lat": -81.148489, "max_lat": -79.224237}, {"s2_cell_id": "13439022763050270720", "record_count": 11840, "min_date": "2025-01-01 02:14:15.278000+00:00", "max_date": "2025-02-09 07:43:10.074000+00:00", "min_lon": -9.001106, "max_lon": 3.541291, "min_lat": -81.148489, "max_lat": -79.224237}, {"s2_cell_id": "13442400462770798592", "record_count": 11136, "min_date": "2025-01-01 03:54:44.437000+00:00", "max_date": "2025-02-09 07:44:08.529000+00:00", "min_lon": -33.001106, "max_lon": -20.431927, "min_lat": -81.518952, "max_lat": -79.23176}, {"s2_cell_id": "13464918460907651072", "record_count": 10592, "min_date": "2025-01-01 07:15:33.113000+00:00", "max_date": "2025-02-09 11:04:56.976000+00:00", "min_lon": -69.001106, "max_lon": -56.431927, "min_lat": -81.687656, "max_lat": -79.398842}, {"s2_cell_id": "13469985010488442880", "record_count": 9136, "min_date": "2025-01-01 05:35:14.848000+00:00", "max_date": "2025-02-09 07:44:57.495000+00:00", "min_lon": -51.001021, "max_lon": -38.4837, "min_lat": -81.148061, "max_lat": -78.993202}, {"s2_cell_id": "13468296160628178944", "record_count": 8592, "min_date": "2025-01-01 05:35:23.745000+00:00", "max_date": "2025-02-09 09:24:47.462000+00:00", "min_lon": -57.327217, "max_lon": -44.426393, "min_lat": -82.044489, "max_lat": -80.116248}, {"s2_cell_id": "13467733210674757632", "record_count": 8496, "min_date": "2025-01-01 05:35:45.802000+00:00", "max_date": "2025-02-09 11:04:35.025000+00:00", "min_lon": -63.001005, "max_lon": -50.4837, "min_lat": -81.148489, "max_lat": -78.571109}, {"s2_cell_id": "12661588877375438848", "record_count": 8496, "min_date": "2025-01-01 17:20:48.068000+00:00", "max_date": "2025-02-07 22:59:59.806000+00:00", "min_lon": 116.998995, "max_lon": 129.5163, "min_lat": -81.148489, "max_lat": -78.429372}, {"s2_cell_id": "13469422060535021568", "record_count": 8208, "min_date": "2025-01-01 05:34:54.171000+00:00", "max_date": "2025-02-09 09:24:24.138000+00:00", "min_lon": -45.363374, "max_lon": -32.398172, "min_lat": -82.044046, "max_lat": -80.116248}, {"s2_cell_id": "12666655426956230656", "record_count": 8176, "min_date": "2025-01-01 19:00:36.279000+00:00", "max_date": "2025-02-08 22:30:09.136000+00:00", "min_lon": 110.998894, "max_lon": 123.568073, "min_lat": -81.563802, "max_lat": -79.268012}, {"s2_cell_id": "13442963412724219904", "record_count": 8032, "min_date": "2025-01-01 03:54:23.601000+00:00", "max_date": "2025-02-09 07:43:44.948000+00:00", "min_lon": -27.446519, "max_lon": -13.931065, "min_lat": -82.044523, "max_lat": -80.116248}, {"s2_cell_id": "13464355510954229760", "record_count": 8000, "min_date": "2025-01-01 07:15:55.599000+00:00", "max_date": "2025-02-09 11:05:17.328000+00:00", "min_lon": -75.447234, "max_lon": -61.931848, "min_lat": -82.044523, "max_lat": -80.116248}, {"s2_cell_id": "11921872638579834880", "record_count": 7968, "min_date": "2025-01-01 12:18:14.429000+00:00", "max_date": "2025-02-09 17:47:19.478000+00:00", "min_lon": -159.001106, "max_lon": -146.431927, "min_lat": -81.739966, "max_lat": -79.269575}, {"s2_cell_id": "12653144628074119168", "record_count": 7968, "min_date": "2025-01-01 15:39:47.365000+00:00", "max_date": "2025-02-09 19:29:06.778000+00:00", "min_lon": 146.998894, "max_lon": 159.568073, "min_lat": -81.517872, "max_lat": -79.224237}, {"s2_cell_id": "13468859110581600256", "record_count": 7888, "min_date": "2025-01-01 05:34:54.536000+00:00", "max_date": "2025-02-09 09:24:43.576000+00:00", "min_lon": -51.631506, "max_lon": -37.128896, "min_lat": -82.840939, "max_lat": -81.007401}, {"s2_cell_id": "12666092477002809344", "record_count": 7856, "min_date": "2025-01-01 19:00:17.148000+00:00", "max_date": "2025-02-09 22:00:18.463000+00:00", "min_lon": 114.837423, "max_lon": 130.068935, "min_lat": -82.83328, "max_lat": -80.399876}, {"s2_cell_id": "13457600111513174016", "record_count": 7760, "min_date": "2025-01-01 07:15:14.651000+00:00", "max_date": "2025-02-09 09:25:07.896000+00:00", "min_lon": -64.935776, "max_lon": -49.90077, "min_lat": -82.841264, "max_lat": -80.351986}, {"s2_cell_id": "11925250338300362752", "record_count": 7584, "min_date": "2025-01-01 13:58:53.538000+00:00", "max_date": "2025-02-08 17:28:32.472000+00:00", "min_lon": -179.895502, "max_lon": -170.431927, "min_lat": -81.148489, "max_lat": -79.224237}, {"s2_cell_id": "12717883872717570048", "record_count": 7552, "min_date": "2025-01-01 02:13:51.062000+00:00", "max_date": "2025-02-09 06:03:14.177000+00:00", "min_lon": -3.001106, "max_lon": 9.568073, "min_lat": -81.148489, "max_lat": -79.224237}, {"s2_cell_id": "13463229611047387136", "record_count": 7296, "min_date": "2025-01-01 07:16:25.005000+00:00", "max_date": "2025-02-09 12:45:11.863000+00:00", "min_lon": -86.277942, "max_lon": -74.431984, "min_lat": -81.148061, "max_lat": -78.844388}]}, "s2_level_analysis": {"4": {"primary_cell": 4300937644138823680, "covering_cells": [4299811744231981056], "performance": {"primary_cell_count": 0, "primary_cell_time_ms": 407.702, "covering_cells_count": 0, "covering_cells_time_ms": 407.702}}, "5": {"primary_cell": 4299811744231981056, "covering_cells": [4300656169162113024], "performance": {"primary_cell_count": 0, "primary_cell_time_ms": 386.70500000000004, "covering_cells_count": 0, "covering_cells_time_ms": 386.70500000000004}}, "6": {"primary_cell": 4300656169162113024, "covering_cells": [4300445062929580032], "performance": {"primary_cell_count": 560, "primary_cell_time_ms": 375.789, "covering_cells_count": 560, "covering_cells_time_ms": 375.789}}, "7": {"primary_cell": 4300445062929580032, "covering_cells": [4300392286371446784, 4300427470743535616], "performance": {"primary_cell_count": 0, "primary_cell_time_ms": 391.339, "covering_cells_count": 0, "covering_cells_time_ms": 523.503}}, "8": {"primary_cell": 4300392286371446784, "covering_cells": [4300396684417957888, 4300405480510980096, 4300414276604002304, 4300440664883068928], "performance": {"primary_cell_count": 0, "primary_cell_time_ms": 380.313, "covering_cells_count": 0, "covering_cells_time_ms": 579.55}}, "9": {"primary_cell": 4300396684417957888, "covering_cells": [4300399982952841216, 4300402181976096768, 4300413177092374528, 4300441764394696704], "performance": {"primary_cell_count": 0, "primary_cell_time_ms": 608.961, "covering_cells_count": 0, "covering_cells_time_ms": 592.3100000000001}}, "10": {"primary_cell": 4300399982952841216, "covering_cells": [4300399158319120384, 4300400807586562048, 4300401357342375936, 4300412902214467584, 4300442039272603648, 4300442589028417536], "performance": {"primary_cell_count": 0, "primary_cell_time_ms": 439.035, "covering_cells_count": 0, "covering_cells_time_ms": 556.4970000000001}}}, "predicate_comparison": {"bbox_only": {"execution_time_ms": 591.797, "rows_returned": 96, "files_scanned": "52/52", "duckdb_total_time": "0.717s", "explain_output": "[EXPLAIN ANALYZE]\n     explain_key                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            explain_value\n0  analyzed_plan  ┌─────────────────────────────────────┐\\n│┌───────────────────────────────────┐│\\n││    Query Profiling Information    ││\\n│└───────────────────────────────────┘│\\n└─────────────────────────────────────┘\\nEXPLAIN ANALYZE              SELECT scene_id, cog_key, datetime, bbox, s2_cell_id             FROM unified_stac              WHERE datetime >= '2025-01-10T00:00:00Z'               AND datetime < '2025-02-15T00:00:00Z'               AND bbox.xmin <= 77.58 AND bbox.ymin <= 13.08                AND bbox.xmax >= 77.55 AND bbox.ymax >= 13.01             LIMIT 100             \\n┌────────────────────────────────────────────────┐\\n│┌──────────────────────────────────────────────┐│\\n││              Total Time: 0.717s              ││\\n│└──────────────────────────────────────────────┘│\\n└────────────────────────────────────────────────┘\\n┌───────────────────────────┐\\n│           QUERY           │\\n└─────────────┬─────────────┘\\n┌─────────────┴─────────────┐\\n│      EXPLAIN_ANALYZE      │\\n│    ────────────────────   │\\n│           0 Rows          │\\n│          (0.00s)          │\\n└─────────────┬─────────────┘\\n┌─────────────┴─────────────┐\\n│         PROJECTION        │\\n│    ────────────────────   │\\n│          scene_id         │\\n│          cog_key          │\\n│          datetime         │\\n│            bbox           │\\n│         s2_cell_id        │\\n│                           │\\n│          96 Rows          │\\n│          (0.00s)          │\\n└─────────────┬─────────────┘\\n┌─────────────┴─────────────┐\\n│      STREAMING_LIMIT      │\\n│    ────────────────────   │\\n│          96 Rows          │\\n│          (0.00s)          │\\n└─────────────┬─────────────┘\\n┌─────────────┴─────────────┐\\n│         TABLE_SCAN        │\\n│    ────────────────────   │\\n│        Projections:       │\\n│          scene_id         │\\n│          datetime         │\\n│            bbox           │\\n│         s2_cell_id        │\\n│          cog_key          │\\n│                           │\\n│          Filters:         │\\n│  datetime>='2025-01-10 00 │\\n│ :00:00+00'::TIMESTAMP WITH│\\n│   TIME ZONE AND datetime< │\\n│ '2025-02-15 00:00:00+00': │\\n│ :TIMESTAMP WITH TIME ZONE │\\n│ bbox.xmin<=77.58 AND bbox │\\n│ .ymin<=13.08 AND bbox.xmax│\\n│ >=77.55 AND bbox.ymax>=13 │\\n│            .01            │\\n│                           │\\n│       File Filters:       │\\n│  datetime>='2025-01-10 00 │\\n│ :00:00+00'::TIMESTAMP WITH│\\n│   TIME ZONE AND datetime< │\\n│ '2025-02-15 00:00:00+00': │\\n│ :TIMESTAMP WITH TIME ZONE │\\n│ bbox.ymax>=13.01 AND bbox │\\n│ .xmax>=77.55 AND bbox.ymin│\\n│ <=13.08 AND bbox.xmin<=77 │\\n│            .58            │\\n│                           │\\n│   Scanning Files: 52/52   │\\n│    Total Files Read: 52   │\\n│                           │\\n│          96 Rows          │\\n│          (0.38s)          │\\n└───────────────────────────┘\\n"}, "s2_primary": {"execution_time_ms": 552.806, "rows_returned": 96, "files_scanned": "40/52", "duckdb_total_time": "0.569s", "explain_output": "[EXPLAIN ANALYZE]\n     explain_key                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  explain_value\n0  analyzed_plan  ┌─────────────────────────────────────┐\\n│┌───────────────────────────────────┐│\\n││    Query Profiling Information    ││\\n│└───────────────────────────────────┘│\\n└─────────────────────────────────────┘\\nEXPLAIN ANALYZE              SELECT scene_id, cog_key, datetime, bbox, s2_cell_id             FROM unified_stac              WHERE datetime >= '2025-01-10T00:00:00Z'               AND datetime < '2025-02-15T00:00:00Z'               AND bbox.xmin <= 77.58 AND bbox.ymin <= 13.08                AND bbox.xmax >= 77.55 AND bbox.ymax >= 13.01               AND s2_cell_id = '4300656169162113024'             LIMIT 100             \\n┌─────────────────────────────────────┐\\n│┌───────────────────────────────────┐│\\n││         HTTPFS HTTP Stats         ││\\n││                                   ││\\n││            in: 0 bytes            ││\\n││            out: 0 bytes           ││\\n││              #HEAD: 0             ││\\n││              #GET: 0              ││\\n││              #PUT: 0              ││\\n││              #POST: 0             ││\\n││             #DELETE: 0            ││\\n│└───────────────────────────────────┘│\\n└─────────────────────────────────────┘\\n┌────────────────────────────────────────────────┐\\n│┌──────────────────────────────────────────────┐│\\n││              Total Time: 0.569s              ││\\n│└──────────────────────────────────────────────┘│\\n└────────────────────────────────────────────────┘\\n┌───────────────────────────┐\\n│           QUERY           │\\n└─────────────┬─────────────┘\\n┌─────────────┴─────────────┐\\n│      EXPLAIN_ANALYZE      │\\n│    ────────────────────   │\\n│           0 Rows          │\\n│          (0.00s)          │\\n└─────────────┬─────────────┘\\n┌─────────────┴─────────────┐\\n│         PROJECTION        │\\n│    ────────────────────   │\\n│          scene_id         │\\n│          cog_key          │\\n│          datetime         │\\n│            bbox           │\\n│         s2_cell_id        │\\n│                           │\\n│          96 Rows          │\\n│          (0.00s)          │\\n└─────────────┬─────────────┘\\n┌─────────────┴─────────────┐\\n│      STREAMING_LIMIT      │\\n│    ────────────────────   │\\n│          96 Rows          │\\n│          (0.00s)          │\\n└─────────────┬─────────────┘\\n┌─────────────┴─────────────┐\\n│         TABLE_SCAN        │\\n│    ────────────────────   │\\n│        Projections:       │\\n│          scene_id         │\\n│          datetime         │\\n│            bbox           │\\n│         s2_cell_id        │\\n│          cog_key          │\\n│                           │\\n│          Filters:         │\\n│  datetime>='2025-01-10 00 │\\n│ :00:00+00'::TIMESTAMP WITH│\\n│   TIME ZONE AND datetime< │\\n│ '2025-02-15 00:00:00+00': │\\n│ :TIMESTAMP WITH TIME ZONE │\\n│ bbox.xmin<=77.58 AND bbox │\\n│ .ymin<=13.08 AND bbox.xmax│\\n│ >=77.55 AND bbox.ymax>=13 │\\n│            .01            │\\n│        s2_cell_id=        │\\n│   '4300656169162113024'   │\\n│                           │\\n│       File Filters:       │\\n│  datetime>='2025-01-10 00 │\\n│ :00:00+00'::TIMESTAMP WITH│\\n│   TIME ZONE AND datetime< │\\n│ '2025-02-15 00:00:00+00': │\\n│ :TIMESTAMP WITH TIME ZONE │\\n│ bbox.ymax>=13.01 AND bbox │\\n│ .xmax>=77.55 AND bbox.ymin│\\n│ <=13.08 AND bbox.xmin<=77 │\\n│            .58            │\\n│        s2_cell_id=        │\\n│   '4300656169162113024'   │\\n│                           │\\n│   Scanning Files: 40/52   │\\n│    Total Files Read: 40   │\\n│                           │\\n│          96 Rows          │\\n│          (0.15s)          │\\n└───────────────────────────┘\\n"}, "s2_with_array": {"execution_time_ms": 621.798, "rows_returned": 96, "files_scanned": "52/52", "duckdb_total_time": "0.634s", "explain_output": "[EXPLAIN ANALYZE]\n     explain_key                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             explain_value\n0  analyzed_plan  ┌─────────────────────────────────────┐\\n│┌───────────────────────────────────┐│\\n││    Query Profiling Information    ││\\n│└───────────────────────────────────┘│\\n└─────────────────────────────────────┘\\nEXPLAIN ANALYZE              SELECT scene_id, cog_key, datetime, bbox, s2_cell_id             FROM unified_stac              WHERE datetime >= '2025-01-10T00:00:00Z'               AND datetime < '2025-02-15T00:00:00Z'               AND bbox.xmin <= 77.58 AND bbox.ymin <= 13.08                AND bbox.xmax >= 77.55 AND bbox.ymax >= 13.01               AND (s2_cell_id = '4300656169162113024' OR list_contains(s2_cells, '4300656169162113024'))             LIMIT 100             \\n┌────────────────────────────────────────────────┐\\n│┌──────────────────────────────────────────────┐│\\n││              Total Time: 0.634s              ││\\n│└──────────────────────────────────────────────┘│\\n└────────────────────────────────────────────────┘\\n┌───────────────────────────┐\\n│           QUERY           │\\n└─────────────┬─────────────┘\\n┌─────────────┴─────────────┐\\n│      EXPLAIN_ANALYZE      │\\n│    ────────────────────   │\\n│           0 Rows          │\\n│          (0.00s)          │\\n└─────────────┬─────────────┘\\n┌─────────────┴─────────────┐\\n│         PROJECTION        │\\n│    ────────────────────   │\\n│          scene_id         │\\n│          cog_key          │\\n│          datetime         │\\n│            bbox           │\\n│         s2_cell_id        │\\n│                           │\\n│          96 Rows          │\\n│          (0.00s)          │\\n└─────────────┬─────────────┘\\n┌─────────────┴─────────────┐\\n│      STREAMING_LIMIT      │\\n│    ────────────────────   │\\n│          96 Rows          │\\n│          (0.00s)          │\\n└─────────────┬─────────────┘\\n┌─────────────┴─────────────┐\\n│         PROJECTION        │\\n│    ────────────────────   │\\n│             #0            │\\n│             #1            │\\n│             #2            │\\n│             #3            │\\n│             #5            │\\n│                           │\\n│          96 Rows          │\\n│          (0.00s)          │\\n└─────────────┬─────────────┘\\n┌─────────────┴─────────────┐\\n│           FILTER          │\\n│    ────────────────────   │\\n│      ((s2_cell_id =       │\\n│ '4300656169162113024') OR │\\n│  list_contains(s2_cells,  │\\n│  '4300656169162113024'))  │\\n│                           │\\n│          96 Rows          │\\n│          (0.00s)          │\\n└─────────────┬─────────────┘\\n┌─────────────┴─────────────┐\\n│         TABLE_SCAN        │\\n│    ────────────────────   │\\n│        Projections:       │\\n│          scene_id         │\\n│          datetime         │\\n│            bbox           │\\n│         s2_cell_id        │\\n│          s2_cells         │\\n│          cog_key          │\\n│                           │\\n│          Filters:         │\\n│  datetime>='2025-01-10 00 │\\n│ :00:00+00'::TIMESTAMP WITH│\\n│   TIME ZONE AND datetime< │\\n│ '2025-02-15 00:00:00+00': │\\n│ :TIMESTAMP WITH TIME ZONE │\\n│ bbox.xmin<=77.58 AND bbox │\\n│ .ymin<=13.08 AND bbox.xmax│\\n│ >=77.55 AND bbox.ymax>=13 │\\n│            .01            │\\n│                           │\\n│       File Filters:       │\\n│  datetime>='2025-01-10 00 │\\n│ :00:00+00'::TIMESTAMP WITH│\\n│   TIME ZONE AND datetime< │\\n│ '2025-02-15 00:00:00+00': │\\n│ :TIMESTAMP WITH TIME ZONE │\\n│ bbox.ymax>=13.01 AND bbox │\\n│ .xmax>=77.55 AND bbox.ymin│\\n│ <=13.08 AND bbox.xmin<=77 │\\n│            .58            │\\n│                           │\\n│   Scanning Files: 52/52   │\\n│    Total Files Read: 52   │\\n│                           │\\n│          96 Rows          │\\n│          (0.30s)          │\\n└───────────────────────────┘\\n"}}}