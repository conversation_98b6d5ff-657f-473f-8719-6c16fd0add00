---
type: "always_apply"
---

Core

Follow established best practices; write small, typed, testable functions.

Be truthful: if unknown, say “I don’t know.” State uncertainty and verify.

No assumptions about configs, env, or intent—check the code/docs first.

Do not re-implement complex internals (e.g., Ray). Prefer official APIs.

Tools & Context

Use MCP servers and available tools to retrieve docs/code before acting.

Always read the full file you’re editing and identify affected imports/uses.

Prefer a services/ layer for external clients/APIs.

Python & Packaging

Use uv for all Python workflows: uv init, uv add, uv lock, uv sync.

Manage deps in pyproject.toml only; no ad-hoc pip install.

Dockerize with uv (cache lock + wheels); pin versions via lockfile.

Edit Workflow (for every change)

Retrieve related files; map dependencies/usages.

Check for an existing function before adding a new one.

Make minimal, reversible diffs; keep API compatibility when feasible.

Verify imports/types; run linters/tests locally.

Explain why a change is needed; show the exact diff or patch.

Responses

Code-first: inspect and run code before claims.

Be specific; no vague guidance. Provide runnable examples.

When an assumption is required, stop, verify with tools, then proceed.

Quality & Safety

Enforce typing, docstrings, and consistent style (ruff/black/mypy/pytest).

Never paste secrets; sanitize inputs; least-privilege configs.

Licensing & Headers

Add at the top of new/modified files:
// SPDX-FileCopyrightText: Terrafloww Labs, 2025

If no license is present, set license = "Terrafloww Labs Proprietary" in project metadata and keep headers consistent.

Never

Hallucinate, oversell, or fabricate results.

Blind-copy code without understanding and citation.

Bypass tests, type checks, or review steps.