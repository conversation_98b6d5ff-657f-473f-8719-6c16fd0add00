# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Quick verification that S2 fix is working and measuring actual performance difference.
"""

import os
import sys
import time
from datetime import datetime

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from data_marketplace.api.duckdb_service import DuckDBService, TableConfig

def test_s2_vs_bbox():
    """Test the exact same queries from our analysis to verify the fix."""
    
    table_path = os.getenv("API_ATTACH_TABLE_PATH", "s3://tf-datalake-bucket/deltalake-tables/unified_stac_table_v2")
    
    service = DuckDBService()
    service.initialize()
    
    # Attach the Delta table
    config = TableConfig(
        name="unified_stac",
        path=table_path,
        alias="unified_stac",
        pin_snapshot=True
    )
    service.attach_table(config)
    
    # Test geometry bounds (Bangalore AOI)
    bbox = (77.55, 13.01, 77.58, 13.08)  # (minx, miny, maxx, maxy)
    
    # S2 cell from our analysis (level 6)
    s2_cell = "4300656169162113024"
    
    queries = {
        "bbox_only": f"""
        SELECT scene_id, collection, datetime, bbox, cog_key, cog_href, 
               cog_href AS request_url, 
               cog_width, cog_height, cog_tile_width, cog_tile_height, cog_crs_code, cog_transform, 
               cog_tile_offsets, cog_tile_byte_counts, cog_dtype_code, cog_bits_per_sample, cog_compression_code, cog_predictor, 
               cog_dn_scale, cog_dn_offset, cog_roles 
        FROM unified_stac 
        WHERE datetime >= '2025-01-10T00:00:00Z'
          AND datetime < '2025-02-15T00:00:00Z'
          AND year = 2025
          AND month IN (1,2)
          AND cog_key IN ('B02','B03','B04','blue','green','red')
          AND bbox.xmin <= {bbox[2]} AND bbox.ymin <= {bbox[3]} 
          AND bbox.xmax >= {bbox[0]} AND bbox.ymax >= {bbox[1]}
        ORDER BY datetime DESC
        LIMIT 8 OFFSET 0
        """,
        
        "s2_primary": f"""
        SELECT scene_id, collection, datetime, bbox, cog_key, cog_href, 
               cog_href AS request_url, 
               cog_width, cog_height, cog_tile_width, cog_tile_height, cog_crs_code, cog_transform, 
               cog_tile_offsets, cog_tile_byte_counts, cog_dtype_code, cog_bits_per_sample, cog_compression_code, cog_predictor, 
               cog_dn_scale, cog_dn_offset, cog_roles 
        FROM unified_stac 
        WHERE datetime >= '2025-01-10T00:00:00Z'
          AND datetime < '2025-02-15T00:00:00Z'
          AND year = 2025
          AND month IN (1,2)
          AND cog_key IN ('B02','B03','B04','blue','green','red')
          AND bbox.xmin <= {bbox[2]} AND bbox.ymin <= {bbox[3]} 
          AND bbox.xmax >= {bbox[0]} AND bbox.ymax >= {bbox[1]}
          AND s2_cell_id IN ('{s2_cell}')
        ORDER BY datetime DESC
        LIMIT 8 OFFSET 0
        """
    }
    
    print("=== VERIFYING S2 FIX ===")
    print(f"Testing with S2 cell: {s2_cell}")
    print(f"Bbox: {bbox}")
    
    results = {}
    
    for query_name, sql in queries.items():
        print(f"\n--- {query_name.upper()} ---")
        
        # Time the query
        start_time = time.time()
        result = service.execute_query(sql)
        execution_time_ms = (time.time() - start_time) * 1000
        
        row_count = len(result.arrow_table)
        
        print(f"Execution time: {execution_time_ms:.1f}ms")
        print(f"Rows returned: {row_count}")
        
        # Get EXPLAIN for file scanning info
        explain_sql = f"EXPLAIN ANALYZE {sql}"
        try:
            explain_result = service.profile_query(sql)
            if explain_result and "raw_explain" in explain_result:
                explain_text = explain_result["raw_explain"]
                
                # Extract files scanned
                import re
                files_match = re.search(r'Scanning Files: (\d+/\d+)', explain_text)
                files_scanned = files_match.group(1) if files_match else "unknown"
                
                # Extract total time
                time_match = re.search(r'Total Time: ([\d.]+)s', explain_text)
                total_time = time_match.group(1) if time_match else "unknown"
                
                print(f"Files scanned: {files_scanned}")
                print(f"DuckDB total time: {total_time}s")
                
                results[query_name] = {
                    "execution_time_ms": execution_time_ms,
                    "rows_returned": row_count,
                    "files_scanned": files_scanned,
                    "duckdb_total_time": total_time
                }
            else:
                print("EXPLAIN failed")
                results[query_name] = {
                    "execution_time_ms": execution_time_ms,
                    "rows_returned": row_count,
                    "files_scanned": "unknown",
                    "duckdb_total_time": "unknown"
                }
        except Exception as e:
            print(f"EXPLAIN error: {e}")
            results[query_name] = {
                "execution_time_ms": execution_time_ms,
                "rows_returned": row_count,
                "files_scanned": "unknown",
                "duckdb_total_time": "unknown"
            }
    
    print(f"\n=== SUMMARY ===")
    if "bbox_only" in results and "s2_primary" in results:
        bbox_time = results["bbox_only"]["execution_time_ms"]
        s2_time = results["s2_primary"]["execution_time_ms"]
        
        bbox_files = results["bbox_only"]["files_scanned"]
        s2_files = results["s2_primary"]["files_scanned"]
        
        print(f"BBOX: {bbox_time:.1f}ms, {bbox_files} files")
        print(f"S2:   {s2_time:.1f}ms, {s2_files} files")
        
        if bbox_time > 0 and s2_time > 0:
            speedup = ((bbox_time - s2_time) / bbox_time) * 100
            print(f"S2 speedup: {speedup:.1f}%")
        
        if bbox_files != "unknown" and s2_files != "unknown":
            print(f"File reduction: {bbox_files} -> {s2_files}")
    
    service.close()
    return results

if __name__ == "__main__":
    test_s2_vs_bbox()
