# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Deep dive analysis of S2 vs BBOX performance using actual Delta Lake data.

This script systematically verifies claims about S2 performance by:
1. Analyzing Delta Lake file-level statistics
2. Examining S2 cell distributions and coverage
3. Testing different S2 cell levels and configurations
4. Measuring actual predicate pushdown effectiveness
"""

import os
import sys
import logging
from typing import Dict, List, Any, Tuple
from datetime import datetime

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from data_marketplace.api.duckdb_service import DuckDBService, TableConfig
from data_marketplace.spatial.s2_utils import S2Utils
from shapely.geometry import Polygon, box

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Test geometry - Bangalore AOI from the example
BANGALORE_POLY = Polygon([
    (77.55, 13.01),
    (77.58, 13.01), 
    (77.58, 13.08),
    (77.55, 13.08),
    (77.55, 13.01),
])

class S2PerformanceAnalyzer:
    """Systematic analysis of S2 vs BBOX performance."""
    
    def __init__(self, table_path: str):
        self.table_path = table_path
        self.service = DuckDBService()
        self.service.initialize()
        
        # Attach the Delta table
        config = TableConfig(
            name="unified_stac",
            path=table_path,
            alias="unified_stac",
            pin_snapshot=True
        )
        self.service.attach_table(config)
        
    def analyze_delta_file_statistics(self) -> Dict[str, Any]:
        """Analyze Delta Lake file-level statistics and partitioning."""
        print("\n=== DELTA LAKE FILE ANALYSIS ===")
        
        # Get basic table info
        table_info = self.service.get_table_info("unified_stac")
        print(f"Table path: {table_info['path']}")
        print(f"Total rows: {table_info['row_count']:,}")
        
        # Analyze file-level statistics
        file_stats_sql = """
        SELECT 
            COUNT(*) as total_files,
            MIN(datetime) as min_datetime,
            MAX(datetime) as max_datetime,
            COUNT(DISTINCT year) as unique_years,
            COUNT(DISTINCT month) as unique_months,
            COUNT(DISTINCT s2_cell_id) as unique_s2_cells,
            MIN(bbox.xmin) as min_lon, MAX(bbox.xmax) as max_lon,
            MIN(bbox.ymin) as min_lat, MAX(bbox.ymax) as max_lat
        FROM unified_stac
        """
        
        result = self.service.execute_query(file_stats_sql)
        stats = result.arrow_table.to_pylist()[0]
        
        print(f"File statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
            
        return stats
    
    def analyze_s2_cell_distribution(self) -> Dict[str, Any]:
        """Analyze S2 cell distribution in the data."""
        print("\n=== S2 CELL DISTRIBUTION ANALYSIS ===")
        
        # Get S2 cell distribution
        s2_dist_sql = """
        SELECT 
            s2_cell_id,
            COUNT(*) as record_count,
            MIN(datetime) as min_date,
            MAX(datetime) as max_date,
            MIN(bbox.xmin) as min_lon, MAX(bbox.xmax) as max_lon,
            MIN(bbox.ymin) as min_lat, MAX(bbox.ymax) as max_lat
        FROM unified_stac 
        GROUP BY s2_cell_id 
        ORDER BY record_count DESC 
        LIMIT 20
        """
        
        result = self.service.execute_query(s2_dist_sql)
        distribution = result.arrow_table.to_pylist()
        
        print("Top 20 S2 cells by record count:")
        for cell in distribution[:10]:
            print(f"  Cell {cell['s2_cell_id']}: {cell['record_count']} records")
            print(f"    Bbox: ({cell['min_lon']:.3f}, {cell['min_lat']:.3f}) to ({cell['max_lon']:.3f}, {cell['max_lat']:.3f})")
            
        return {"distribution": distribution}
    
    def test_s2_cell_levels(self, geometry: Polygon) -> Dict[int, Dict[str, Any]]:
        """Test different S2 cell levels for the given geometry."""
        print(f"\n=== S2 CELL LEVEL ANALYSIS ===")
        print(f"Test geometry bounds: {geometry.bounds}")
        
        results = {}
        s2_utils = S2Utils()
        
        # Test levels 4-10
        for level in range(4, 11):
            print(f"\nTesting S2 level {level}:")
            
            # Generate S2 cells for this level
            s2_utils.cell_level = level
            centroid = geometry.centroid
            primary_cell = s2_utils.point_to_s2_cell(centroid.x, centroid.y, level=level)
            
            # Also get covering cells
            covering_cells = s2_utils.polygon_to_s2_cells(geometry, level=level, max_cells=10)
            
            print(f"  Primary cell (centroid): {primary_cell}")
            print(f"  Covering cells ({len(covering_cells)}): {covering_cells[:5]}{'...' if len(covering_cells) > 5 else ''}")
            
            # Test query performance with this cell
            perf_result = self._test_s2_query_performance(primary_cell, covering_cells)
            
            results[level] = {
                "primary_cell": primary_cell,
                "covering_cells": covering_cells,
                "performance": perf_result
            }
            
        return results
    
    def _test_s2_query_performance(self, primary_cell: int, covering_cells: List[int]) -> Dict[str, Any]:
        """Test query performance for specific S2 cells."""
        
        # Test 1: Primary cell only
        primary_sql = f"""
        SELECT COUNT(*) as count
        FROM unified_stac 
        WHERE datetime >= '2025-01-10T00:00:00Z'
          AND datetime < '2025-02-15T00:00:00Z'
          AND s2_cell_id = '{primary_cell}'
        """
        
        start_time = datetime.now()
        primary_result = self.service.execute_query(primary_sql)
        primary_time = (datetime.now() - start_time).total_seconds() * 1000
        primary_count = primary_result.arrow_table.to_pylist()[0]['count']
        
        # Test 2: Covering cells with IN clause
        if len(covering_cells) > 1:
            cells_str = "', '".join(str(c) for c in covering_cells)
            covering_sql = f"""
            SELECT COUNT(*) as count
            FROM unified_stac 
            WHERE datetime >= '2025-01-10T00:00:00Z'
              AND datetime < '2025-02-15T00:00:00Z'
              AND s2_cell_id IN ('{cells_str}')
            """
            
            start_time = datetime.now()
            covering_result = self.service.execute_query(covering_sql)
            covering_time = (datetime.now() - start_time).total_seconds() * 1000
            covering_count = covering_result.arrow_table.to_pylist()[0]['count']
        else:
            covering_time = primary_time
            covering_count = primary_count
        
        print(f"    Primary cell: {primary_count} records in {primary_time:.1f}ms")
        print(f"    Covering cells: {covering_count} records in {covering_time:.1f}ms")
        
        return {
            "primary_cell_count": primary_count,
            "primary_cell_time_ms": primary_time,
            "covering_cells_count": covering_count,
            "covering_cells_time_ms": covering_time
        }
    
    def compare_predicate_pushdown(self) -> Dict[str, Any]:
        """Compare predicate pushdown effectiveness between S2 and BBOX."""
        print(f"\n=== PREDICATE PUSHDOWN COMPARISON ===")
        
        # Get the S2 cell for our test geometry
        s2_utils = S2Utils(cell_level=6)
        centroid = BANGALORE_POLY.centroid
        s2_cell = s2_utils.point_to_s2_cell(centroid.x, centroid.y, level=6)
        
        bbox = BANGALORE_POLY.bounds  # (minx, miny, maxx, maxy)
        
        queries = {
            "bbox_only": f"""
            SELECT scene_id, cog_key, datetime, bbox, s2_cell_id
            FROM unified_stac 
            WHERE datetime >= '2025-01-10T00:00:00Z'
              AND datetime < '2025-02-15T00:00:00Z'
              AND bbox.xmin <= {bbox[2]} AND bbox.ymin <= {bbox[3]} 
              AND bbox.xmax >= {bbox[0]} AND bbox.ymax >= {bbox[1]}
            LIMIT 100
            """,
            
            "s2_primary": f"""
            SELECT scene_id, cog_key, datetime, bbox, s2_cell_id
            FROM unified_stac 
            WHERE datetime >= '2025-01-10T00:00:00Z'
              AND datetime < '2025-02-15T00:00:00Z'
              AND bbox.xmin <= {bbox[2]} AND bbox.ymin <= {bbox[3]} 
              AND bbox.xmax >= {bbox[0]} AND bbox.ymax >= {bbox[1]}
              AND s2_cell_id = '{s2_cell}'
            LIMIT 100
            """,
            
            "s2_with_array": f"""
            SELECT scene_id, cog_key, datetime, bbox, s2_cell_id
            FROM unified_stac 
            WHERE datetime >= '2025-01-10T00:00:00Z'
              AND datetime < '2025-02-15T00:00:00Z'
              AND bbox.xmin <= {bbox[2]} AND bbox.ymin <= {bbox[3]} 
              AND bbox.xmax >= {bbox[0]} AND bbox.ymax >= {bbox[1]}
              AND (s2_cell_id = '{s2_cell}' OR list_contains(s2_cells, '{s2_cell}'))
            LIMIT 100
            """
        }
        
        results = {}
        
        for query_name, sql in queries.items():
            print(f"\nTesting {query_name}:")
            
            # Execute with EXPLAIN ANALYZE
            explain_sql = f"EXPLAIN ANALYZE {sql}"
            explain_result = self.service.profile_query(sql)
            
            # Execute actual query for timing
            start_time = datetime.now()
            result = self.service.execute_query(sql)
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            row_count = len(result.arrow_table)
            
            print(f"  Execution time: {execution_time:.1f}ms")
            print(f"  Rows returned: {row_count}")
            
            if explain_result and "raw_explain" in explain_result:
                explain_text = explain_result["raw_explain"]
                # Extract key metrics from EXPLAIN
                files_scanned = self._extract_files_scanned(explain_text)
                total_time = self._extract_total_time(explain_text)
                
                print(f"  Files scanned: {files_scanned}")
                print(f"  DuckDB total time: {total_time}")
                
                results[query_name] = {
                    "execution_time_ms": execution_time,
                    "rows_returned": row_count,
                    "files_scanned": files_scanned,
                    "duckdb_total_time": total_time,
                    "explain_output": explain_text
                }
            else:
                results[query_name] = {
                    "execution_time_ms": execution_time,
                    "rows_returned": row_count,
                    "files_scanned": "unknown",
                    "duckdb_total_time": "unknown"
                }
        
        return results
    
    def _extract_files_scanned(self, explain_text: str) -> str:
        """Extract files scanned info from EXPLAIN output."""
        import re
        match = re.search(r'Scanning Files: (\d+/\d+)', explain_text)
        return match.group(1) if match else "unknown"
    
    def _extract_total_time(self, explain_text: str) -> str:
        """Extract total time from EXPLAIN output."""
        import re
        match = re.search(r'Total Time: ([\d.]+)s', explain_text)
        return f"{match.group(1)}s" if match else "unknown"
    
    def run_full_analysis(self) -> Dict[str, Any]:
        """Run complete S2 performance analysis."""
        print("Starting comprehensive S2 vs BBOX performance analysis...")
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "test_geometry_bounds": BANGALORE_POLY.bounds,
        }
        
        try:
            # 1. Delta Lake file analysis
            results["delta_statistics"] = self.analyze_delta_file_statistics()
            
            # 2. S2 cell distribution
            results["s2_distribution"] = self.analyze_s2_cell_distribution()
            
            # 3. S2 cell level testing
            results["s2_level_analysis"] = self.test_s2_cell_levels(BANGALORE_POLY)
            
            # 4. Predicate pushdown comparison
            results["predicate_comparison"] = self.compare_predicate_pushdown()
            
            print(f"\n=== ANALYSIS COMPLETE ===")
            print("Key findings will be summarized...")
            
        except Exception as e:
            logger.error(f"Analysis failed: {e}", exc_info=True)
            results["error"] = str(e)
        
        finally:
            self.service.close()
            
        return results

def main():
    """Main analysis function."""
    table_path = os.getenv("API_ATTACH_TABLE_PATH", "s3://tf-datalake-bucket/deltalake-tables/unified_stac_table_v2")
    
    if not table_path:
        print("Error: API_ATTACH_TABLE_PATH environment variable not set")
        sys.exit(1)
    
    analyzer = S2PerformanceAnalyzer(table_path)
    results = analyzer.run_full_analysis()
    
    # Save results to file
    import json
    output_file = "s2_analysis_results.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\nResults saved to {output_file}")

if __name__ == "__main__":
    main()
