# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Rewrite a Delta Lake table to a new location with optimized Parquet writer settings.

Goals
- Preserve schema and partitions
- Write with smaller Parquet row groups (default: 32MB) for better DuckDB pruning
- Stream by partitions (year/month[/day]) to avoid large memory usage
- Optional: compact and Z-order after rewrite

Usage
  uv run python scripts/rewrite_delta_table.py \
    --src s3://bucket/path/unified_stac_table \
    --dst s3://bucket/path/unified_stac_table_rewritten \
    --granularity day \
    --row-group-mb 32 \
    --page-mb 1 \
    --optimize --zorder s2_cell_id

Notes
- Requires AWS creds available to deltalake/httpfs (env/instance role)
- We read via DuckDB (delta_scan) and write via deltalake.write_deltalake
- We preserve partitions year/month (and day if present)
"""

from __future__ import annotations

import argparse
import gc
import logging
import os
from typing import Iterable, List, Tuple

import duckdb  # type: ignore
import pyarrow as pa
from deltalake import DeltaTable, write_deltalake, WriterProperties  # type: ignore

# Prefer our project settings if available, but keep script standalone-safe
try:
    from data_marketplace.config.parquet_optimization import (
        ParquetOptimizationConfig as POC,
    )
except Exception:  # pragma: no cover - best effort fallback
    class POC:
        ROW_GROUP_SIZE = 32 * 1024 * 1024
        DATA_PAGE_SIZE = 1 * 1024 * 1024


logger = logging.getLogger("rewrite-delta")
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")


def _duckdb_conn(storage_options: dict | None = None) -> duckdb.DuckDBPyConnection:
    conn = duckdb.connect()
    conn.execute("INSTALL delta")
    conn.execute("LOAD delta")

    # Configure DuckDB for low memory environment (following working patterns)
    conn.execute("SET memory_limit='5.5GB'")
    conn.execute("SET threads=2")
    conn.execute("SET preserve_insertion_order=false")

    # Configure S3 credentials following working pattern from clean_delta_table.py
    if storage_options and "AWS_REGION" in storage_options:
        region = storage_options["AWS_REGION"]
        if "AWS_ACCESS_KEY_ID" in storage_options:
            conn.execute(f"""
                CREATE SECRET s3_secret (
                    TYPE S3,
                    KEY_ID '{storage_options["AWS_ACCESS_KEY_ID"]}',
                    SECRET '{storage_options["AWS_SECRET_ACCESS_KEY"]}',
                    REGION '{region}'
                )
            """)
            logger.info(f"Configured DuckDB S3 access with explicit credentials for region {region}")
        else:
            conn.execute(f"""
                CREATE SECRET s3_secret (
                    TYPE S3,
                    PROVIDER credential_chain,
                    REGION '{region}'
                )
            """)
            logger.info(f"Configured DuckDB S3 access with credential chain for region {region}")

        # Also set session-level region
        try:
            conn.execute(f"SET s3_region='{region}'")
        except Exception:
            pass

    return conn


action_info = """
This tool will:
- Read source table partitions using DuckDB delta_scan
- Write to destination Delta table with specified Parquet writer properties
- Use append mode in small batches to avoid high memory
- Optionally compact and Z-order the destination table
"""


def discover_partitions(conn: duckdb.DuckDBPyConnection, src: str, has_day: bool) -> List[Tuple[int, int, int | None]]:
    """Return list of (year, month, day?) partitions available in the source table.
    Caller must provide whether a 'day' column exists to avoid DESCRIBE on delta_scan().
    """
    if has_day:
        q = f"""
        SELECT year, month, day
        FROM delta_scan('{src}')
        GROUP BY year, month, day
        ORDER BY year, month, day
        """
    else:
        q = f"""
        SELECT year, month, NULL::INTEGER as day
        FROM delta_scan('{src}')
        GROUP BY year, month
        ORDER BY year, month
        """

    parts = [(int(y), int(m), int(d) if d is not None else None) for y, m, d in conn.execute(q).fetchall()]
    return parts


def get_existing_partitions(dst: str, storage_options: dict | None) -> set:
    """Get existing partitions in destination table to support restart."""
    try:
        dt = DeltaTable(dst, storage_options=storage_options)
        conn = _duckdb_conn(storage_options)

        # Query existing partitions
        existing_query = f"""
        SELECT DISTINCT year, month, day
        FROM delta_scan('{dst}')
        """
        existing = conn.execute(existing_query).fetchall()
        conn.close()

        # Convert to set of tuples for fast lookup
        existing_set = {(int(y), int(m), int(d) if d is not None else None) for y, m, d in existing}
        logger.info(f"Found {len(existing_set)} existing partitions in destination table")
        return existing_set

    except Exception as e:
        logger.info(f"Destination table doesn't exist or is empty: {e}")
        return set()


def clear_memory():
    """Force garbage collection and clear memory."""
    gc.collect()
    # Force PyArrow to release memory
    try:
        pa.default_memory_pool().release_unused()
    except:
        pass


def ensure_dest_table(dst: str, schema: pa.Schema, partition_by: List[str], storage_options: dict | None) -> None:
    """Check if destination table exists, skip creation - let write_deltalake handle it."""
    try:
        DeltaTable(dst, storage_options=storage_options)
        logger.info("Destination table exists: %s", dst)
        return
    except Exception:
        logger.info("Destination table will be created on first write: %s", dst)
        # Skip table creation - let write_deltalake handle it with the first batch
        return


def iter_batches(
    conn: duckdb.DuckDBPyConnection,
    src: str,
    year: int,
    month: int,
    day: int | None,
    batch_limit: int,
) -> Iterable[pa.Table]:
    """Yield Arrow tables for the given partition in small LIMIT/OFFSET batches.
    This prevents reading the entire partition in one go.
    """
    base_where = f"year = {year} AND month = {month}"
    if day is not None:
        base_where += f" AND day = {day}"

    # Count rows first for batch planning (fast using stats)
    total_rows = conn.execute(
        f"SELECT COUNT(*) FROM delta_scan('{src}') WHERE {base_where}"
    ).fetchone()[0]

    if total_rows == 0:
        return

    offset = 0
    while offset < total_rows:
        q = (
            f"SELECT * FROM delta_scan('{src}') WHERE {base_where} "
            f"ORDER BY datetime ASC LIMIT {batch_limit} OFFSET {offset}"
        )
        tbl = conn.execute(q).fetch_arrow_table()
        if len(tbl) == 0:
            break
        yield tbl
        offset += len(tbl)


def rewrite(
    src: str,
    dst: str,
    granularity: str,
    row_group_mb: int,
    page_mb: int,
    batch_rows: int,
    optimize: bool,
    zorder_cols: List[str] | None,
    storage_options: dict | None,
    overwrite_last: bool = False,
):
    conn = _duckdb_conn(storage_options)

    # Read source schema via DeltaTable to preserve types
    src_dt = DeltaTable(src, storage_options=storage_options)
    src_schema = src_dt.schema().to_arrow()

    # Partition columns
    partition_by = ["year", "month"]
    has_day = any(f.name == "day" for f in src_schema)
    if has_day and granularity == "day":
        partition_by.append("day")

    ensure_dest_table(dst, src_schema, partition_by, storage_options)

    # Writer properties with smaller row groups
    writer_props = WriterProperties(
        max_row_group_size=row_group_mb * 1024 * 1024,
        data_page_size_limit=page_mb * 1024 * 1024,
    )

    parts = discover_partitions(conn, src, has_day)
    logger.info("Discovered %d partitions", len(parts))

    # Check for existing partitions to support restart
    existing_partitions = get_existing_partitions(dst, storage_options)

    # If overwrite_last is True, remove the last partition from existing set
    if overwrite_last and existing_partitions:
        last_partition = max(existing_partitions)  # Get the latest partition
        existing_partitions.discard(last_partition)
        logger.info(f"🔄 Will overwrite last partition: {last_partition}")

    written = 0
    first_write = len(existing_partitions) == 0  # Only true if destination is empty
    skipped_count = 0

    for (y, m, d) in parts:
        part_str = f"{y}-{m:02d}" + (f"-{d:02d}" if d is not None else "")
        partition_tuple = (y, m, d)

        # Skip if partition already exists (restart support)
        if partition_tuple in existing_partitions:
            skipped_count += 1
            logger.info(f"⏭️  Skipping existing partition {part_str} ({skipped_count} skipped so far)")
            continue

        logger.info(f"🔄 Rewriting partition {part_str}")
        partition_rows = 0

        for batch_num, batch in enumerate(iter_batches(conn, src, y, m, d, batch_rows)):
            # Use overwrite for first batch to create table, then append
            mode = "overwrite" if first_write else "append"

            write_deltalake(
                table_or_uri=dst,
                data=batch,
                mode=mode,
                partition_by=partition_by,
                storage_options=storage_options,
                writer_properties=writer_props,
            )

            partition_rows += len(batch)
            written += len(batch)
            first_write = False

            # Clear memory after each batch to prevent OOM
            clear_memory()

            if batch_num % 5 == 0:  # Log every 5 batches
                logger.info(f"  📝 Batch {batch_num + 1}: {len(batch)} rows (partition total: {partition_rows})")

        logger.info(f"✅ Partition {part_str} complete: {partition_rows} rows (total written: {written:,})")

        # Force memory cleanup after each partition
        clear_memory()

    # Optional optimize step (compaction & z-order)
    if optimize:
        try:
            dt = DeltaTable(dst, storage_options=storage_options)
            logger.info("Running compact() on destination table...")
            dt.optimize.compact(max_concurrent_tasks=2)
            if zorder_cols:
                logger.info("Running z_order(%s) on destination table...", ",".join(zorder_cols))
                dt.optimize.z_order(zorder_cols, max_concurrent_tasks=2)
            logger.info("Optimize complete")
        except Exception as e:  # best effort
            logger.warning("Optimize step failed: %s", e)

    logger.info("Rewrite complete. Total rows written: %d", written)


def parse_args() -> argparse.Namespace:
    p = argparse.ArgumentParser(description="Rewrite/optimize a Delta table")
    p.add_argument("--src", required=True, help="Source Delta table URI (s3://...)")
    p.add_argument("--dst", required=True, help="Destination Delta table URI (s3://...)")
    p.add_argument(
        "--granularity",
        choices=["month", "day"],
        default="day",
        help="Partition rewrite granularity (day yields smaller batches)",
    )
    p.add_argument("--row-group-mb", type=int, default=POC.ROW_GROUP_SIZE // (1024 * 1024))
    p.add_argument("--page-mb", type=int, default=POC.DATA_PAGE_SIZE // (1024 * 1024))
    p.add_argument("--batch-rows", type=int, default=15_000, help="Rows per write batch (reduced for memory safety)")
    p.add_argument("--optimize", action="store_true", help="Run compact + optional Z-order")
    p.add_argument("--zorder", type=str, help="Comma-separated Z-order columns (e.g., s2_cell_id)")
    p.add_argument("--overwrite-last", action="store_true", help="Overwrite the last partition for safety (prevents partial writes)")
    return p.parse_args()


def get_storage_options(s3_path: str) -> dict:
    """Get storage options for S3 paths."""
    storage_options = {}
    if s3_path.startswith("s3://"):
        bucket_name = s3_path.replace("s3://", "").split("/")[0]
        # Auto-detect region from bucket
        try:
            import subprocess
            result = subprocess.run(
                ["aws", "s3api", "get-bucket-location", "--bucket", bucket_name],
                capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                import json
                location = json.loads(result.stdout).get("LocationConstraint")
                region = location if location else "us-east-1"
                storage_options["AWS_REGION"] = region
                logger.info(f"Detected S3 bucket '{bucket_name}' in region: {region}")
            else:
                storage_options["AWS_REGION"] = "us-west-2"  # Default
                logger.warning(f"Could not detect bucket region, using default: us-west-2")
        except Exception as e:
            storage_options["AWS_REGION"] = "us-west-2"  # Default
            logger.warning(f"Error detecting bucket region: {e}, using default: us-west-2")

        storage_options["AWS_S3_ALLOW_UNSAFE_RENAME"] = "true"
        logger.info("Using IAM role or AWS CLI profile for S3 access")

    return storage_options


def main() -> None:
    args = parse_args()

    storage_options = get_storage_options(args.src)
    zorder_cols = [c.strip() for c in args.zorder.split(",")] if args.zorder else None

    logger.info(action_info)
    logger.info(
        "Source: %s\nDestination: %s\nRow group: %d MB, Page: %d MB, Batch rows: %d",
        args.src,
        args.dst,
        args.row_group_mb,
        args.page_mb,
        args.batch_rows,
    )

    rewrite(
        src=args.src,
        dst=args.dst,
        granularity=args.granularity,
        row_group_mb=args.row_group_mb,
        page_mb=args.page_mb,
        batch_rows=args.batch_rows,
        optimize=bool(args.optimize),
        zorder_cols=zorder_cols,
        storage_options=storage_options,
        overwrite_last=args.overwrite_last,
    )


if __name__ == "__main__":
    main()

