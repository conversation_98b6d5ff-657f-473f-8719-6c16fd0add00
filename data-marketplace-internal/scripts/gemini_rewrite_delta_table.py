# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
One-time script to rewrite a Delta Lake table with optimized properties.

This script reads an existing Delta table, rewrites it to a new location with
optimized Parquet settings (e.g., row group size, bloom filters), and then
applies Z-Ordering to the new table for enhanced query performance.

Example Usage:
python scripts/rewrite_delta_table.py \
    s3://my-bucket/path/to/source_table \
    s3://my-bucket/path/to/target_table \
    --zorder-by "s2_cell_id,datetime"
"""

import argparse
import logging
import os
import subprocess
import sys
import time
from datetime import datetime

import pyarrow as pa
from deltalake import DeltaTable, write_deltalake

from data_marketplace.config.parquet_optimization import (
    get_delta_lake_properties,
    get_optimized_stac_settings,
)
from data_marketplace.ingestion.stac_schema import UnifiedStacSchema

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def get_bucket_region(bucket_name: str) -> str:
    """
    Get the actual region of an S3 bucket using AWS CLI.
    (Reused from ingest_stac_unified.py)
    """
    try:
        result = subprocess.run(
            ["aws", "s3api", "get-bucket-location", "--bucket", bucket_name],
            capture_output=True,
            text=True,
            timeout=10,
        )
        if result.returncode == 0:
            import json

            location = json.loads(result.stdout).get("LocationConstraint")
            return location or "us-east-1"
        else:
            logger.warning(f"Could not get bucket region: {result.stderr}")
            return "us-west-2"  # fallback
    except Exception as e:
        logger.warning(f"Error getting bucket region: {e}")
        return "us-west-2"  # fallback


def get_storage_options(output_path: str) -> dict:
    """
    Get storage options for Delta Lake based on the output path.
    (Reused from ingest_stac_unified.py)
    """
    storage_options = {}
    if output_path.startswith("s3://"):
        bucket_name = output_path.replace("s3://", "").split("/")[0]
        aws_region = get_bucket_region(bucket_name)
        logger.info(f"Detected S3 bucket '{bucket_name}' in region: {aws_region}")
        storage_options["AWS_REGION"] = aws_region
        storage_options["AWS_S3_ALLOW_UNSAFE_RENAME"] = "true"

        aws_access_key = os.getenv("AWS_ACCESS_KEY_ID")
        aws_secret_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        if aws_access_key and aws_secret_key:
            storage_options["AWS_ACCESS_KEY_ID"] = aws_access_key
            storage_options["AWS_SECRET_ACCESS_KEY"] = aws_secret_key
            if os.getenv("AWS_SESSION_TOKEN"):
                storage_options["AWS_SESSION_TOKEN"] = os.getenv("AWS_SESSION_TOKEN")
            logger.info("Using AWS credentials from environment variables")
        else:
            logger.info("Using IAM role or AWS CLI profile")
    return storage_options


def main():
    """Main rewrite function."""
    parser = argparse.ArgumentParser(
        description="Rewrite a Delta Lake table with optimized settings and Z-Ordering."
    )
    parser.add_argument("source_path", help="S3 URI of the source Delta table.")
    parser.add_argument("target_path", help="S3 URI for the new, rewritten Delta table.")
    parser.add_argument(
        "--zorder-by",
        default="s2_cell_id,datetime",
        help="Comma-separated list of columns to Z-Order by (e.g., 's2_cell_id,datetime').",
    )
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)",
    )
    args = parser.parse_args()

    logging.getLogger().setLevel(getattr(logging, args.log_level))
    start_time = time.time()

    logger.info("🚀 Starting Delta Table Rewrite")
    logger.info(f"Source: {args.source_path}")
    logger.info(f"Target: {args.target_path}")
    logger.info(f"Z-Order By: {args.zorder_by}")
    logger.info("=" * 60)

    storage_options = get_storage_options(args.source_path)

    try:
        # 1. Read source table
        read_start = time.time()
        logger.info(f"Reading source Delta table from {args.source_path}...")
        source_dt = DeltaTable(args.source_path, storage_options=storage_options)
        source_schema = source_dt.schema().to_arrow()
        source_dataset = source_dt.to_pyarrow_dataset()
        logger.info(
            f"Source table read as a PyArrow dataset in {time.time() - read_start:.2f}s."
        )

        # 2. Get optimized write settings from the project's config
        delta_properties = get_delta_lake_properties()
        # Convert any integer values to strings for Delta Lake configuration
        delta_properties = {k: str(v) for k, v in delta_properties.items()}
        parquet_settings = get_optimized_stac_settings()
        # Ensure the unified schema's dictionary columns are used
        unified_schema_def = UnifiedStacSchema()
        parquet_settings["use_dictionary"] = unified_schema_def.get_dictionary_columns()
        logger.info(f"Applying optimized Parquet settings (Row Group Size: {parquet_settings.get('row_group_size') / (1024*1024)}MB)")

        # 3. Write to target table in chunks
        write_start = time.time()
        logger.info(f"Writing to new Delta table at {args.target_path} in chunks...")
        
        total_rows = 0
        batches = source_dataset.to_batches()
        
        # Write the first batch with "overwrite" mode to create the table
        try:
            first_batch = next(batches)
            total_rows += len(first_batch)
            
            # Convert first batch to a table to pass table_properties
            first_table = pa.Table.from_batches([first_batch])

            write_deltalake(
                args.target_path,
                first_table,
                mode="overwrite",
                partition_by=["year", "month"],
                configuration=delta_properties,
                writer_properties=parquet_settings,
                storage_options=storage_options,
            )
            logger.info(f"Wrote first chunk of {len(first_batch)} rows.")
        except StopIteration:
            logger.warning("Source table is empty. Creating an empty target table.")
            write_deltalake(
                args.target_path,
                source_dt.to_pyarrow_table(), # Empty table
                mode="overwrite",
                partition_by=["year", "month"],
                configuration=delta_properties,
                writer_properties=parquet_settings,
                storage_options=storage_options,
            )


        # Append the rest of the batches
        for i, batch in enumerate(batches):
            total_rows += len(batch)
            write_deltalake(
                args.target_path,
                batch,
                mode="append",
                writer_properties=parquet_settings,
                storage_options=storage_options,
            )
            logger.info(f"Wrote chunk {i+2} with {len(batch)} rows.")

        logger.info(f"Finished writing {total_rows:,} rows in {time.time() - write_start:.2f}s.")

        # 4. Optimize (Z-Order) and Vacuum
        optimize_start = time.time()
        logger.info(f"Loading new table at {args.target_path} for optimization...")
        target_dt = DeltaTable(args.target_path, storage_options=storage_options)

        if args.zorder_by:
            zorder_columns = [c.strip() for c in args.zorder_by.split(",")]
            logger.info(f"Applying Z-Order by: {zorder_columns}")
            target_dt.optimize.zorder(zorder_columns)
            logger.info("Z-Ordering complete.")
        else:
            logger.info("Skipping Z-Ordering as no columns were specified.")

        logger.info("Vacuuming table to remove old files from overwrite...")
        target_dt.vacuum(dry_run=False, retention_hours=0)
        logger.info("Vacuum complete.")
        logger.info(f"Optimization and vacuum finished in {time.time() - optimize_start:.2f}s.")

        total_duration = time.time() - start_time
        logger.info("=" * 60)
        logger.info(f"✅ Rewrite complete in {total_duration:.2f} seconds!")
        logger.info(f"The new, optimized table is ready at: {args.target_path}")

    except Exception as e:
        logger.error(f"Rewrite failed: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()

