# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Delta Lake table management utility for cleanup and optimization operations.

This module provides reusable utilities for Delta Lake table maintenance
including vacuum operations, optimization/compaction, and file cleanup.
"""

import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class DeltaTableManager:
    """
    Utility class for Delta Lake table maintenance operations.
    
    Provides methods for:
    - Table optimization and compaction
    - Vacuum operations for old/orphan file cleanup
    - Combined cleanup operations
    - Safe retention period enforcement
    """

    @staticmethod
    async def perform_table_cleanup(
        table_path: str,
        storage_options: Dict[str, Any],
        skip_vacuum: bool = False,
        skip_optimize: bool = False,
        vacuum_retention_hours: int = 168
    ) -> None:
        """
        Perform comprehensive table cleanup operations: optimize and vacuum.
        
        Args:
            table_path: Path to the Delta table
            storage_options: Storage configuration for the table
            skip_vacuum: Skip vacuum cleanup of old/orphan files
            skip_optimize: Skip table optimization/compaction
            vacuum_retention_hours: Hours to retain old files before vacuum cleanup
        """
        try:
            from deltalake import DeltaTable

            # Load the Delta table for cleanup operations
            dt = DeltaTable(table_path, storage_options=storage_options)

            # 1. Optimize/Compact the table (combine small files)
            if not skip_optimize:
                await DeltaTableManager.optimize_table(dt)
            else:
                logger.info("⏭️  Skipping table optimization")

            # 2. Vacuum old and orphan files
            if not skip_vacuum:
                await DeltaTableManager.vacuum_table(dt, vacuum_retention_hours)
            else:
                logger.info("⏭️  Skipping vacuum cleanup")

        except Exception as e:
            logger.error(f"❌ Table cleanup failed: {e}")
            # Don't raise - cleanup failures shouldn't stop the ingestion

    @staticmethod
    async def optimize_table(dt) -> Optional[Dict[str, Any]]:
        """
        Optimize/compact a Delta table by combining small files.
        
        Args:
            dt: DeltaTable instance
            
        Returns:
            Optimization result dictionary or None if failed
        """
        logger.info("🔧 Optimizing table (compacting small files)...")
        try:
            optimize_result = dt.optimize.compact(max_concurrent_tasks=2)  # Memory-conservative concurrency
            files_added = optimize_result.get('files_added', 0)
            files_removed = optimize_result.get('files_removed', 0)
            logger.info(f"✅ Table optimized: {files_removed} small files → {files_added} optimized files")
            return optimize_result
        except Exception as e:
            logger.warning(f"⚠️  Table optimization failed: {e}")
            return None

    @staticmethod
    async def vacuum_table(dt, vacuum_retention_hours: int = 168) -> Optional[int]:
        """
        Vacuum old and orphan files from a Delta table.
        
        Args:
            dt: DeltaTable instance
            vacuum_retention_hours: Hours to retain old files before cleanup
            
        Returns:
            Number of files deleted or None if failed
        """
        logger.info(f"🧹 Vacuuming old files (retention: {vacuum_retention_hours} hours)...")
        try:
            # Enforce minimum safe retention (7 days = 168 hours)
            safe_retention_hours = max(vacuum_retention_hours, 168)
            if vacuum_retention_hours < 168:
                logger.warning(f"⚠️  Vacuum retention increased from {vacuum_retention_hours}h to {safe_retention_hours}h for safety")

            # First do a dry run to see what would be deleted
            dry_run_files = dt.vacuum(
                retention_hours=safe_retention_hours,
                dry_run=True
            )
            logger.info(f"📋 Found {len(dry_run_files)} old/orphan files to clean up")

            if dry_run_files:
                # Log some examples of files to be deleted for transparency
                example_files = dry_run_files[:3]  # Show first 3 files
                logger.info(f"📋 Example files to delete: {example_files}")

                # Actually delete the files
                deleted_files = dt.vacuum(
                    retention_hours=safe_retention_hours,
                    dry_run=False
                )
                logger.info(f"✅ Vacuum completed: {len(deleted_files)} old/orphan files deleted")
                return len(deleted_files)
            else:
                logger.info("✅ No old/orphan files to clean up")
                return 0
        except Exception as e:
            logger.warning(f"⚠️  Vacuum operation failed: {e}")
            return None

    @staticmethod
    def enforce_safe_retention_hours(vacuum_retention_hours: int, minimum_hours: int = 168) -> int:
        """
        Enforce minimum safe retention period for vacuum operations.
        
        Args:
            vacuum_retention_hours: Requested retention hours
            minimum_hours: Minimum safe retention hours (default: 168 = 7 days)
            
        Returns:
            Safe retention hours (at least minimum_hours)
        """
        safe_retention_hours = max(vacuum_retention_hours, minimum_hours)
        if vacuum_retention_hours < minimum_hours:
            logger.warning(f"⚠️  Vacuum retention increased from {vacuum_retention_hours}h to {safe_retention_hours}h for safety")
        return safe_retention_hours

    @staticmethod
    async def optimize_table_by_path(
        table_path: str,
        storage_options: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Optimize a Delta table by path (convenience method).
        
        Args:
            table_path: Path to the Delta table
            storage_options: Storage configuration for the table
            
        Returns:
            Optimization result dictionary or None if failed
        """
        try:
            from deltalake import DeltaTable
            dt = DeltaTable(table_path, storage_options=storage_options)
            return await DeltaTableManager.optimize_table(dt)
        except Exception as e:
            logger.error(f"❌ Failed to optimize table at {table_path}: {e}")
            return None

    @staticmethod
    async def vacuum_table_by_path(
        table_path: str,
        storage_options: Dict[str, Any],
        vacuum_retention_hours: int = 168
    ) -> Optional[int]:
        """
        Vacuum a Delta table by path (convenience method).
        
        Args:
            table_path: Path to the Delta table
            storage_options: Storage configuration for the table
            vacuum_retention_hours: Hours to retain old files before cleanup
            
        Returns:
            Number of files deleted or None if failed
        """
        try:
            from deltalake import DeltaTable
            dt = DeltaTable(table_path, storage_options=storage_options)
            return await DeltaTableManager.vacuum_table(dt, vacuum_retention_hours)
        except Exception as e:
            logger.error(f"❌ Failed to vacuum table at {table_path}: {e}")
            return None
