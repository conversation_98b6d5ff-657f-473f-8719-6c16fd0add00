# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
Reusable band alias utilities for mapping user-friendly band codes
(e.g., B02, B8A, B11, SCL) to canonical cog_key names used in tables
(e.g., blue, nir08, swir16, scl).

Keep these mappings small, typed, and testable.
"""
from __future__ import annotations

from typing import Dict, List

# Canonical mappings for common collections
_SENTINEL2_BANDS: Dict[str, str] = {
    "B01": "coastal",
    "B02": "blue",
    "B03": "green",
    "B04": "red",
    "B05": "rededge1",
    "B06": "rededge2",
    "B07": "rededge3",
    "B08": "nir",
    "B8A": "nir08",
    "B09": "nir09",
    "B11": "swir16",
    "B12": "swir22",
    "SCL": "scl",
}

_LANDSAT9_BANDS: Dict[str, str] = {
    "B1": "coastal",
    "B2": "blue",
    "B3": "green",
    "B4": "red",
    "B5": "nir08",
    "B6": "swir16",
    "B7": "swir22",
    "QA_AEROSOL": "qa_aerosol",
    "QA_PIXEL": "qa_pixel",
    "QA_RADSAT": "qa_radsat",
}

# Registry by collection identifier
BAND_MAPS: Dict[str, Dict[str, str]] = {
    # Sentinel-2
    "sentinel-2-l2a": _SENTINEL2_BANDS,
    "sentinel2": _SENTINEL2_BANDS,
    "s2": _SENTINEL2_BANDS,
    "sentinel-2": _SENTINEL2_BANDS,
    # Landsat 9
    "landsat-c2l2-sr": _LANDSAT9_BANDS,
    "landsat9": _LANDSAT9_BANDS,
    "landsat": _LANDSAT9_BANDS,
}


def normalize_assets(dataset: str, assets: str) -> str:
    """Normalize assets list (comma-separated) to canonical cog_key values.

    - Accepts band codes like B02,B8A,B11,SCL for Sentinel-2 and B1..B7 for Landsat9
    - Also allows canonical names (blue, red, nir, swir16, ...); they pass through
    - Case-insensitive for band codes; output is a comma-separated string
    """
    ds_key = (dataset or "").strip().lower()
    band_map = BAND_MAPS.get(ds_key)
    # Also try exact/upper variants if provided
    if band_map is None:
        band_map = BAND_MAPS.get((dataset or "").strip())
    if band_map is None:
        band_map = BAND_MAPS.get((dataset or "").strip().upper())

    upper_key_map: Dict[str, str] = {k.upper(): v for k, v in (band_map or {}).items()}

    tokens: List[str] = [t.strip() for t in (assets or "").split(",") if t.strip()]
    normalized: List[str] = []
    for tok in tokens:
        mapped = upper_key_map.get(tok.upper())
        normalized.append(mapped if mapped else tok)

    return ",".join(normalized)

