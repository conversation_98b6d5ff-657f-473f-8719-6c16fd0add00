# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Pydantic models for the Delta Lake querying API.
"""

from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, validator


class ResponseFormat(str, Enum):
    """Supported response formats."""
    ARROW = "arrow"
    JSON = "json"


class QueryRequest(BaseModel):
    """Request model for query execution."""
    sql: str = Field(..., description="SQL query to execute")
    parameters: Optional[Dict[str, Any]] = Field(
        default=None, 
        description="Parameters for query templating"
    )
    timeout: Optional[float] = Field(
        default=300.0, 
        ge=1.0, 
        le=3600.0,
        description="Query timeout in seconds (1-3600)"
    )
    
    @validator('sql')
    def validate_sql(cls, v):
        if not v or not v.strip():
            raise ValueError("SQL query cannot be empty")
        return v.strip()


class QueryMetrics(BaseModel):
    """Query execution metrics."""
    execution_time_ms: float = Field(..., description="Query execution time in milliseconds")
    rows_returned: int = Field(..., description="Number of rows returned")
    bytes_scanned: int = Field(..., description="Bytes scanned during query")
    snapshot_version: Optional[int] = Field(None, description="Delta table snapshot version")


class QueryResponse(BaseModel):
    """Response model for JSON format queries."""
    data: List[Dict[str, Any]] = Field(..., description="Query result data")
    metrics: QueryMetrics = Field(..., description="Query execution metrics")
    columns: List[str] = Field(..., description="Column names")


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str = Field(..., description="Error message")
    error_type: str = Field(..., description="Type of error")
    query: Optional[str] = Field(None, description="Query that caused the error")


class TableInfo(BaseModel):
    """Information about an attached table."""
    alias: str = Field(..., description="Table alias")
    path: str = Field(..., description="Delta table path")
    table_schema: List[Dict[str, str]] = Field(..., description="Table schema", alias="schema")
    row_count: int = Field(..., description="Approximate row count")
    pin_snapshot: bool = Field(..., description="Whether snapshot is pinned")


class HealthResponse(BaseModel):
    """Health check response."""
    status: str = Field(..., description="Service status")
    version: str = Field(..., description="API version")
    duckdb_version: str = Field(..., description="DuckDB version")
    attached_tables: List[str] = Field(..., description="List of attached table aliases")
    pool_size: int = Field(..., description="Connection pool size")


class NamedQuery(BaseModel):
    """Named query definition."""
    name: str = Field(..., description="Query name")
    sql: str = Field(..., description="SQL template")
    description: Optional[str] = Field(None, description="Query description")
    parameters: Optional[List[str]] = Field(None, description="Required parameters")
    
    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError("Query name cannot be empty")
        # Only allow alphanumeric and underscore
        if not v.replace('_', '').isalnum():
            raise ValueError("Query name can only contain letters, numbers, and underscores")
        return v.strip()


class QueryTemplate:
    """Utility class for SQL query templating."""
    
    @staticmethod
    def substitute_parameters(sql: str, parameters: Optional[Dict[str, Any]] = None) -> str:
        """
        Substitute parameters in SQL query using simple string replacement.
        
        Parameters are expected to be in the format {param_name}.
        This is a simple implementation - for production use, consider
        using a more robust templating engine like Jinja2.
        """
        if not parameters:
            return sql
            
        result = sql
        for key, value in parameters.items():
            placeholder = f"{{{key}}}"
            if placeholder in result:
                # Basic type handling
                if isinstance(value, str):
                    # Escape single quotes and wrap in quotes
                    escaped_value = value.replace("'", "''")
                    result = result.replace(placeholder, f"'{escaped_value}'")
                elif isinstance(value, (list, tuple)):
                    # Convert list to SQL IN clause format
                    if all(isinstance(item, str) for item in value):
                        formatted_items = [f"'{item.replace(chr(39), chr(39)+chr(39))}'" for item in value]
                    else:
                        formatted_items = [str(item) for item in value]
                    result = result.replace(placeholder, f"({', '.join(formatted_items)})")
                else:
                    # Numbers, booleans, etc.
                    result = result.replace(placeholder, str(value))
        
        return result
    
    @staticmethod
    def validate_query_safety(sql: str) -> None:
        """
        Basic SQL safety validation.
        
        This is a simple whitelist approach. For production use,
        consider using a proper SQL parser and more sophisticated validation.
        """
        sql_upper = sql.upper().strip()
        
        # Only allow SELECT statements
        if not sql_upper.startswith('SELECT'):
            raise ValueError("Only SELECT statements are allowed")
        
        # Disallow dangerous keywords
        dangerous_keywords = [
            'DROP', 'DELETE', 'INSERT', 'UPDATE', 'CREATE', 'ALTER',
            'TRUNCATE', 'REPLACE', 'MERGE', 'COPY', 'LOAD', 'INSTALL'
        ]
        
        for keyword in dangerous_keywords:
            if keyword in sql_upper:
                raise ValueError(f"Keyword '{keyword}' is not allowed")
        
        # Basic length check
        if len(sql) > 10000:
            raise ValueError("Query is too long (max 10000 characters)")


# Predefined named queries for common use cases
PREDEFINED_QUERIES = {
    "spatial_bbox": NamedQuery(
        name="spatial_bbox",
        sql="""
        SELECT scene_id, cog_key, bbox, datetime, collection
        FROM {table_alias}
        WHERE bbox && ST_MakeEnvelope({min_lon}, {min_lat}, {max_lon}, {max_lat}, 4326)
        AND datetime BETWEEN {start_date} AND {end_date}
        ORDER BY datetime DESC
        LIMIT {limit}
        """,
        description="Query scenes within a bounding box and date range",
        parameters=["table_alias", "min_lon", "min_lat", "max_lon", "max_lat", "start_date", "end_date", "limit"]
    ),
    
    "collection_summary": NamedQuery(
        name="collection_summary",
        sql="""
        SELECT 
            collection,
            COUNT(*) as scene_count,
            MIN(datetime) as earliest_date,
            MAX(datetime) as latest_date,
            COUNT(DISTINCT scene_id) as unique_scenes
        FROM {table_alias}
        WHERE collection IN {collections}
        GROUP BY collection
        ORDER BY scene_count DESC
        """,
        description="Get summary statistics for specified collections",
        parameters=["table_alias", "collections"]
    ),
    
    "recent_scenes": NamedQuery(
        name="recent_scenes",
        sql="""
        SELECT scene_id, collection, datetime, bbox, cog_key
        FROM {table_alias}
        WHERE datetime >= {since_date}
        ORDER BY datetime DESC
        LIMIT {limit}
        """,
        description="Get recent scenes since a specific date",
        parameters=["table_alias", "since_date", "limit"]
    )
}
