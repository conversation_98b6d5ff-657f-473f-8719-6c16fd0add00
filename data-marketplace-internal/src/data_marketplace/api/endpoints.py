# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
FastAPI endpoints for Terrafloww Data API.

Public endpoints return Apache Arrow IPC streams for simple dataset queries.
Admin endpoints provide table attachment/refresh and health checks.
"""

import io
import json
import logging
import os
import time
from typing import Dict, List, Optional, Any, Tu<PERSON>
import re
from datetime import datetime, timedel<PERSON>

import pyarrow as pa
import pyarrow.ipc as ipc
from fastapi import FastAPI, HTTPException, Query, Depends, Request
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware

from shapely import wkb as shapely_wkb
from data_marketplace.spatial.s2_utils import S2Utils

from data_marketplace.api.duckdb_service import DuckDBService, TableConfig, SafetyLimits
from data_marketplace.api.models import (
    QueryRequest, QueryResponse, QueryMetrics, ErrorResponse,
    TableInfo, HealthResponse, ResponseFormat, QueryTemplate,
    PREDEFINED_QUERIES
)
from data_marketplace.config.settings import Settings

logger = logging.getLogger(__name__)

# Global service instance
_duckdb_service: Optional[DuckDBService] = None
# Global table path resolved at startup
_TABLE_PATH: Optional[str] = None


def get_duckdb_service() -> DuckDBService:
    """Dependency to get the DuckDB service instance."""
    global _duckdb_service
    if _duckdb_service is None:
        raise HTTPException(status_code=503, detail="Service not initialized")
    return _duckdb_service


def create_app(settings: Optional[Settings] = None) -> FastAPI:
    """Create and configure the FastAPI application."""
    if settings is None:
        settings = Settings()

    app = FastAPI(
        title="Terrafloww Data API",
        description="High-performance API for querying datasets and returning Apache Arrow streams",
        version="0.1.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )

    # Add CORS middleware
    if settings.api.enable_cors:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # Configure appropriately for production
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

    # Initialize global service with safety limits
    global _duckdb_service
    safety_limits = SafetyLimits(
        max_result_rows=int(os.getenv("MAX_RESULT_ROWS", "1000000")),
        max_result_bytes=int(os.getenv("MAX_RESULT_BYTES", str(100 * 1024 * 1024))),
        max_query_time_seconds=float(os.getenv("MAX_QUERY_TIME_SECONDS", "300")),
        max_concurrent_queries=int(os.getenv("MAX_CONCURRENT_QUERIES", "8")),
        queue_timeout_seconds=float(os.getenv("QUEUE_TIMEOUT_SECONDS", "30"))
    )
    _duckdb_service = DuckDBService(settings=settings, pool_size=4, safety_limits=safety_limits)

    @app.on_event("startup")
    async def startup_event():
        """Initialize the DuckDB service on startup."""
        logger.info("Initializing DuckDB service...")
        _duckdb_service.initialize()

        # Attach default tables if configured
        # This would typically be configured via environment variables
        # or a configuration file in production
        # Auto-attach table from environment if configured
        alias = os.getenv("API_ATTACH_TABLE_ALIAS")
        path = os.getenv("API_ATTACH_TABLE_PATH")
        if alias and path:
            try:
                logger.info(f"Auto-attaching table alias={alias} path={path}")
                table_config = TableConfig(name=alias, path=path, alias=alias, pin_snapshot=True)
                _duckdb_service.attach_table(table_config)
                logger.info(f"Attached table '{alias}' successfully")
            except Exception as e:
                logger.error(f"Failed to auto-attach table alias={alias}: {e}")

        logger.info("DuckDB service initialized successfully")

    @app.on_event("shutdown")
    async def shutdown_event():
        """Clean up resources on shutdown."""
        logger.info("Shutting down DuckDB service...")
        if _duckdb_service:
            _duckdb_service.close()
        logger.info("DuckDB service shut down")

    return app


def create_arrow_streaming_response(arrow_table: pa.Table, metrics: QueryMetrics) -> StreamingResponse:
    """Create a streaming response with Apache Arrow IPC format."""

    def generate_arrow_stream():
        # Create a buffer to write the Arrow stream
        buffer = io.BytesIO()

        # Write the Arrow table as IPC stream
        with ipc.new_stream(buffer, arrow_table.schema) as writer:
            writer.write_table(arrow_table)

        # Add metrics as metadata in the stream
        buffer.seek(0)
        yield buffer.getvalue()

    return StreamingResponse(
        generate_arrow_stream(),
        media_type="application/vnd.apache.arrow.stream",
        headers={
            "X-Query-Time-Ms": str(metrics.execution_time_ms),
            "X-Rows-Returned": str(metrics.rows_returned),
            "X-Bytes-Scanned": str(metrics.bytes_scanned),
        }
    )


# API Endpoints

app = create_app()

# Utilities
DATE_ONLY_RE = re.compile(r"^\d{4}-\d{2}-\d{2}$")

def _normalize_date_range(date_start: str, date_end: str) -> Tuple[str, str, datetime, datetime]:
    """Normalize date range strings to ISO timestamps and return parsed datetimes.
    - If both inputs are date-only (YYYY-MM-DD): interpret range as [start_dayT00Z, end_dayT00Z)
      If start == end, use end = start + 1 day.
    - If inputs include time, use them as-is.
    Returns (start_iso, end_iso, start_dt, end_dt)
    """
    def parse_iso(s: str) -> datetime:
        s = s.strip()
        if s.endswith('Z'):
            return datetime.fromisoformat(s.replace('Z', '+00:00'))
        try:
            return datetime.fromisoformat(s)
        except ValueError:
            # fallback to date-only
            return datetime.strptime(s, '%Y-%m-%d')

    if DATE_ONLY_RE.match(date_start) and DATE_ONLY_RE.match(date_end):
        start_dt = datetime.strptime(date_start, '%Y-%m-%d')
        if date_end == date_start:
            end_dt = start_dt + timedelta(days=1)
        else:
            end_dt = datetime.strptime(date_end, '%Y-%m-%d')
        start_iso = start_dt.strftime('%Y-%m-%dT00:00:00Z')
        end_iso = end_dt.strftime('%Y-%m-%dT00:00:00Z')
        return start_iso, end_iso, start_dt, end_dt

    # Mixed or full ISO
    start_dt = parse_iso(date_start)
    end_dt = parse_iso(date_end)
    # Render back to ISO with Z if timezone-aware UTC, otherwise naive assumed UTC
    def to_iso_z(dt: datetime) -> str:
        # Keep simple: format as '%Y-%m-%dT%H:%M:%SZ' assuming UTC
        return dt.strftime('%Y-%m-%dT%H:%M:%SZ')

    return to_iso_z(start_dt), to_iso_z(end_dt), start_dt, end_dt

# Reuse band alias utilities
from data_marketplace.utils.band_alias import normalize_assets as _normalize_assets


@app.get("/health", response_model=HealthResponse)
async def health_check(service: DuckDBService = Depends(get_duckdb_service)):
    """Health check endpoint."""
    try:
        import duckdb as _duckdb  # type: ignore
        duckdb_version = _duckdb.__version__
    except Exception:
        duckdb_version = "unknown"
    try:
        return HealthResponse(
            status="healthy",
            version="0.1.0",
            duckdb_version=duckdb_version,
            attached_tables=list(service.attached_tables.keys()),
            pool_size=service.pool.pool_size,
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")

# Internal flag to hide admin endpoints from OpenAPI
HIDE_INTERNAL = True



# Public dataset discovery (JSON)
from data_marketplace.api.datasets import list_datasets, get_table_alias


@app.get("/v1/datasets")
async def get_datasets():
    return {"datasets": list_datasets()}


# Simple filter models (Pydantic kept minimal to avoid bloat)
from pydantic import BaseModel
from typing import Optional


class SceneFilters(BaseModel):
    dataset: str
    date_start: str
    date_end: str
    bbox: Optional[str] = None  # "minx,miny,maxx,maxy"
    cloud_cover_max: Optional[float] = None
    limit: Optional[int] = 10000
    offset: Optional[int] = 0


@app.get("/v1/scenes")
async def get_scenes(
    dataset: str,
    date_start: str,
    date_end: str,
    bbox: Optional[str] = None,
    geometry_wkb: Optional[str] = None,
    cloud_cover_max: Optional[float] = None,
    limit: int = 10000,
    offset: int = 0,
    fmt: ResponseFormat = Query(ResponseFormat.ARROW, description="Response format"),
    service: DuckDBService = Depends(get_duckdb_service),
):
    """Return scenes as Arrow IPC stream."""
    try:
        # Resolve table path: try attached tables first, then environment
        table_path = None
        alias = get_table_alias(dataset)
        if alias in service.attached_tables:
            table_path = service.attached_tables[alias].path
        if not table_path:
            table_path = os.getenv("API_ATTACH_TABLE_PATH")
        if not table_path:
            raise HTTPException(status_code=500, detail="Table path not configured (set API_ATTACH_TABLE_PATH or use /tables/attach)")
        # Normalize dates (support date-only semantics) and add partition pruning
        start_iso, end_iso, start_dt, end_dt = _normalize_date_range(date_start, date_end)
        where = [
            f"datetime >= '{start_iso}' AND datetime < '{end_iso}'"
        ]
        # Partition pruning by year/month
        if start_dt.year == end_dt.year:
            where.append(f"year = {start_dt.year}")
            if start_dt.month == end_dt.month:
                where.append(f"month = {start_dt.month}")
            else:
                months = list(range(start_dt.month, end_dt.month + 1))
                where.append(f"month IN ({','.join(map(str, months))})")
        else:
            years = list(range(start_dt.year, end_dt.year + 1))
            where.append(f"year IN ({','.join(map(str, years))})")
        if cloud_cover_max is not None:
            where.append(f"cloud_cover <= {float(cloud_cover_max)}")
        # Optional bbox string filter (minx,miny,maxx,maxy)
        if bbox:
            try:
                parts = [float(x) for x in bbox.split(',')]
                if len(parts) == 4:
                    min_x, min_y, max_x, max_y = parts
                    where.append(
                        f"bbox.xmin <= {max_x} AND bbox.ymin <= {max_y} AND bbox.xmax >= {min_x} AND bbox.ymax >= {min_y}"
                    )
            except Exception:
                pass
        if geometry_wkb:
            try:
                geom = shapely_wkb.loads(bytes.fromhex(geometry_wkb))
                # Add bbox prefilter before S2
                min_x, min_y, max_x, max_y = geom.bounds
                where.append(
                    f"bbox.xmin <= {max_x} AND bbox.ymin <= {max_y} AND bbox.xmax >= {min_x} AND bbox.ymax >= {min_y}"
                )
                level = int(os.getenv("SPATIAL_S2_CELL_LEVEL", "6"))
                s2 = S2Utils(cell_level=level, adaptive_levels=True)
                cells = s2.bbox_to_s2_cells(geom.bounds, level=level, max_cells=100)
                if cells:
                    cells_str = ",".join([f"'{str(c)}'" for c in cells])
                    # Avoid UNNEST on s2_cells to reduce risk of engine instability; optional array filter via env
                    if os.getenv("SPATIAL_S2_ARRAY_FILTER", "false").lower() == "true":
                        arr_filters = " OR ".join([f"list_contains(s2_cells, '{str(c)}')" for c in cells])
                        where.append(f"(s2_cell_id IN ({cells_str}) OR ({arr_filters}))")
                    else:
                        where.append(f"s2_cell_id IN ({cells_str})")
            except Exception:
                raise HTTPException(status_code=400, detail="Invalid geometry_wkb (hex WKB expected)")
        sql = (
            "SELECT scene_id, collection, datetime, bbox "
            f"FROM delta_scan('{table_path}') "
            f"WHERE {' AND '.join(where)} "
            f"ORDER BY datetime DESC "
            f"LIMIT {int(max(0, limit))} OFFSET {int(max(0, offset))}"
        )
        logger.info(f"Generated SQL: {sql}")
        logger.info(f"WHERE clauses: {where}")
        result = service.execute_query(sql)
        # Adapt bbox struct -> fixed 4 list in post step if needed (first version: pass-through)
        metrics = QueryMetrics(
            execution_time_ms=result.execution_time_ms,
            rows_returned=len(result.arrow_table),
            bytes_scanned=result.bytes_scanned,
            snapshot_version=result.snapshot_version,
        )
        if fmt == ResponseFormat.ARROW:
            return create_arrow_streaming_response(result.arrow_table, metrics)
        else:
            data = result.arrow_table.to_pylist()
            columns = result.arrow_table.column_names
            return QueryResponse(data=data, metrics=metrics, columns=columns)
    except KeyError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"/v1/scenes failed: {e}")
        raise HTTPException(status_code=500, detail="Internal error")


@app.get("/v1/scenes:assets")
async def get_scene_assets(
    dataset: str,
    date_start: str,
    date_end: str,
    bbox: Optional[str] = None,
    geometry_wkb: Optional[str] = None,
    cloud_cover_max: Optional[float] = None,
    assets: Optional[str] = None,
    limit: int = 10000,
    offset: int = 0,
    s2_disable: bool = Query(False, description="If true, skip S2 filtering and use bbox-only"),
    explain: bool = Query(False, description="If true, run EXPLAIN ANALYZE (FORMAT json) and emit summary headers"),
    fmt: ResponseFormat = Query(ResponseFormat.ARROW, description="Response format"),
    service: DuckDBService = Depends(get_duckdb_service),
):
    """Return per-asset rows (Arrow IPC) sourced directly from Delta cog_* fields."""
    try:
        # Resolve table path: try attached tables first, then environment
        table_path = None
        alias = get_table_alias(dataset)
        logger.info(f"Looking for alias '{alias}' in attached_tables: {list(service.attached_tables.keys())}")
        if alias in service.attached_tables:
            table_path = service.attached_tables[alias].path
            logger.info(f"Found table_path from attached_tables: {table_path}")
        if not table_path:
            table_path = os.getenv("API_ATTACH_TABLE_PATH")
            logger.info(f"Fallback to env API_ATTACH_TABLE_PATH: {table_path}")
        if not table_path:
            raise HTTPException(status_code=500, detail="Table path not configured (set API_ATTACH_TABLE_PATH or use /tables/attach)")
        # Normalize dates and partition pruning
        start_iso, end_iso, start_dt, end_dt = _normalize_date_range(date_start, date_end)

        where = [
            f"datetime >= '{start_iso}' AND datetime < '{end_iso}'"
        ]
        s2_clause = None  # track S2 clause for optional fallback

        # Add partition pruning for year/month
        if start_dt.year == end_dt.year:
            where.append(f"year = {start_dt.year}")
            if start_dt.month == end_dt.month:
                where.append(f"month = {start_dt.month}")
            else:
                months = list(range(start_dt.month, end_dt.month + 1))
                where.append(f"month IN ({','.join(map(str, months))})")
        else:
            years = list(range(start_dt.year, end_dt.year + 1))
            where.append(f"year IN ({','.join(map(str, years))})")

        # Optional bbox string filter (minx,miny,maxx,maxy)
        if bbox:
            try:
                parts = [float(x) for x in bbox.split(',')]
                if len(parts) == 4:
                    min_x, min_y, max_x, max_y = parts
                    where.append(
                        f"bbox.xmin <= {max_x} AND bbox.ymin <= {max_y} AND bbox.xmax >= {min_x} AND bbox.ymax >= {min_y}"
                    )
            except Exception:
                pass

        if cloud_cover_max is not None:
            where.append(f"cloud_cover <= {float(cloud_cover_max)}")
        if assets:
            # Expand assets to include both canonical names and STAC band codes present in the table
            try:
                from data_marketplace.utils.band_alias import BAND_MAPS as _BAND_MAPS  # local import to avoid cycles
            except Exception:
                _BAND_MAPS = {}
            try:
                assets_norm = _normalize_assets(dataset, assets)
            except Exception:
                assets_norm = assets
            # Tokens user asked for (may be canonical or codes)
            requested = [t.strip() for t in (assets or "").split(",") if t.strip()]
            canonical = [t.strip() for t in (assets_norm or "").split(",") if t.strip()]
            allowed: set[str] = set(canonical) | set(requested)
            # Add corresponding STAC codes for canonical tokens using reverse map
            band_map = _BAND_MAPS.get((dataset or "").strip().lower()) or _BAND_MAPS.get((dataset or "").strip())
            if band_map:
                # reverse: canonical -> [codes]
                rev: dict[str, list[str]] = {}
                for code, canon in band_map.items():
                    rev.setdefault(canon, []).append(code)
                for tok in canonical:
                    for code in rev.get(tok, []):
                        allowed.add(code)
            keys = ",".join([f"'{k}'" for k in sorted(allowed)])
            if keys:
                where.append(f"cog_key IN ({keys})")
        if geometry_wkb:
            try:
                logger.debug(f"Parsing geometry_wkb: {geometry_wkb[:50]}...")
                geom = shapely_wkb.loads(bytes.fromhex(geometry_wkb))
                min_x, min_y, max_x, max_y = geom.bounds
                logger.debug(f"Geometry bounds: {min_x}, {min_y}, {max_x}, {max_y}")

                # Add bbox prefilter before expensive S2 operations
                where.append(
                    f"bbox.xmin <= {max_x} AND bbox.ymin <= {max_y} AND "
                    f"bbox.xmax >= {min_x} AND bbox.ymax >= {min_y}"
                )

                # Add S2 filter after bbox prefilter - match ingestion approach
                if not s2_disable:
                    level = int(os.getenv("SPATIAL_S2_CELL_LEVEL", "6"))
                    s2 = S2Utils(cell_level=level, adaptive_levels=False)

                    # Use centroid approach to match ingestion (point_to_s2_cell at exact level)
                    centroid = geom.centroid
                    primary_cell = s2.point_to_s2_cell(centroid.x, centroid.y, level=level)
                    cells = [primary_cell]
                    logger.info(f"Generated {len(cells)} S2 cells using centroid approach")

                    if cells:
                        cells_str = ",".join([f"'{str(c)}'" for c in cells])
                        # Avoid UNNEST on s2_cells to reduce risk of engine instability; optional array filter via env
                        if os.getenv("SPATIAL_S2_ARRAY_FILTER", "false").lower() == "true":
                            arr_filters = " OR ".join([f"list_contains(s2_cells, '{str(c)}')" for c in cells])
                            s2_clause = f"(s2_cell_id IN ({cells_str}) OR ({arr_filters}))"
                        else:
                            s2_clause = f"s2_cell_id IN ({cells_str})"
                        where.append(s2_clause)
                else:
                    logger.info("S2 filtering disabled via s2_disable=true")
                    s2_clause = None
            except Exception as e:
                logger.error(f"Geometry parsing error: {e}")
                raise HTTPException(status_code=400, detail="Invalid geometry_wkb (hex WKB expected)")
        sql = (
            "SELECT scene_id, collection, datetime, bbox, cog_key, cog_href, "
            "cog_href AS request_url, "
            "cog_width, cog_height, cog_tile_width, cog_tile_height, cog_crs_code, cog_transform, "
            "cog_tile_offsets, cog_tile_byte_counts, cog_dtype_code, cog_bits_per_sample, cog_compression_code, cog_predictor, "
            "cog_dn_scale, cog_dn_offset, cog_roles "
            f"FROM delta_scan('{table_path}') "
            f"WHERE {' AND '.join(where)} "
            f"ORDER BY datetime DESC "
            f"LIMIT {int(max(0, limit))} OFFSET {int(max(0, offset))}"
        )
        logger.debug(f"Generated SQL: {sql}")
        logger.debug(f"WHERE clauses: {where}")
        result = service.execute_query(sql)
        table = result.arrow_table
        # Optional fallback: if geometry + S2 filter produced 0 rows, retry with only bbox filter
        try:
            if geometry_wkb and s2_clause and len(table) == 0 and os.getenv("SPATIAL_S2_FALLBACK_BBOX", "true").lower() == "true":
                where_no_s2 = [w for w in where if w != s2_clause]
                sql2 = (
                    "SELECT scene_id, collection, datetime, bbox, cog_key, cog_href, "
                    "cog_href AS request_url, "
                    "cog_width, cog_height, cog_tile_width, cog_tile_height, cog_crs_code, cog_transform, "
                    "cog_tile_offsets, cog_tile_byte_counts, cog_dtype_code, cog_bits_per_sample, cog_compression_code, cog_predictor, "
                    "cog_dn_scale, cog_dn_offset, cog_roles "
                    f"FROM delta_scan('{table_path}') "
                    f"WHERE {' AND '.join(where_no_s2)} "
                    f"ORDER BY datetime DESC "
                    f"LIMIT {int(max(0, limit))} OFFSET {int(max(0, offset))}"
                )
                logger.info("S2 filter yielded 0 rows; retrying with bbox-only filter")
                result = service.execute_query(sql2)
                table = result.arrow_table
        except Exception as _e:
            # Keep original result if fallback fails
            pass
        # Always provide 4-element transform that rasteret expects: [scale_x, translate_x, scale_y, translate_y]
        try:
            if "cog_transform" in table.column_names:
                transforms = table.column("cog_transform").to_pylist()
                out = []
                for t in transforms:
                    if t and isinstance(t, (list, tuple)) and len(t) >= 6:
                        try:
                            # Convert any 6-element transform to rasteret's 4-element format
                            gt0, gt1, gt2, gt3, gt4, gt5 = [float(x) for x in t[:6]]
                            # Patterns to detect:
                            # 1) v2 matrix style: [sx, shx, shy, sy, tx, ty]
                            if gt1 == 0.0 and gt2 == 0.0 and gt0 != 0.0 and gt3 != 0.0 and gt4 != 0.0:
                                out.append([gt0, gt4, gt3, gt5])
                            # 2) Legacy encoding: [sx, 0, tx, 0, sy, ty]
                            elif gt1 == 0.0 and gt3 == 0.0 and gt0 != 0.0 and gt4 != 0.0:
                                out.append([gt0, gt2, gt4, gt5])
                            else:
                                # 3) GDAL [tx, sx, 0, ty, 0, sy]
                                out.append([gt1, gt0, gt5, gt3])
                        except Exception:
                            out.append(None)
                    else:
                        out.append(None)

                transform_type = pa.list_(pa.float64(), 4)  # fixed-size list of length 4
                transform_arr = pa.array(out, type=transform_type)
                table = table.append_column("transform", transform_arr)
        except Exception as e:
            logger.warning(f"Transform derivation failed: {e}")
            # Continue without transform column
        # Optional EXPLAIN ANALYZE profiling
        explain_metrics = {}
        def _summarize_explain_json(node: Any) -> Dict[str, Any]:
            """Traverse EXPLAIN JSON to estimate rows/bytes/files and total timing.
            Conservative: only aggregates known numeric keys or obvious markers; avoids leaking internals.
            """
            totals = {
                "latency_ms": 0.0,
                "rows_read": 0,
                "bytes_read": 0,
                "files": set(),
            }
            def _walk(x: Any):
                if isinstance(x, dict):
                    for k, v in x.items():
                        kl = str(k).lower()
                        if kl in ("timing", "time", "total_time", "execution_time") and isinstance(v, (int, float)):
                            totals["latency_ms"] += float(v)
                        if ("rows" in kl or kl.endswith("_rows")) and isinstance(v, (int, float)):
                            totals["rows_read"] += int(v)
                        if ("byte" in kl or kl.endswith("_bytes")) and isinstance(v, (int, float)):
                            totals["bytes_read"] += int(v)
                        if kl in ("file", "filename") and isinstance(v, str) and v:
                            totals["files"].add(v)
                        if kl in ("files", "file_names") and isinstance(v, list):
                            for fv in v:
                                if isinstance(fv, str) and fv:
                                    totals["files"].add(fv)
                        _walk(v)
                elif isinstance(x, list):
                    for it in x:
                        _walk(it)
            _walk(node)
            return {
                "latency_ms": round(totals["latency_ms"], 3) if totals["latency_ms"] else None,
                "rows_read": totals["rows_read"] or None,
                "bytes_read": totals["bytes_read"] or None,
                "files_read": len(totals["files"]) or None,
            }
        if explain:
            try:
                # Profile current mode
                profile_main = service.profile_query(sql)
                if profile_main:
                    summarize_main = _summarize_explain_json(profile_main)
                    explain_metrics.update({
                        "latency": summarize_main.get("latency_ms"),
                        "rows_read": summarize_main.get("rows_read"),
                        "bytes_read": summarize_main.get("bytes_read"),
                        "files_read": summarize_main.get("files_read"),
                    })
                    logger.info(f"[PROFILE current] time_ms={summarize_main.get('latency_ms')} rows={summarize_main.get('rows_read')} bytes={summarize_main.get('bytes_read')} files={summarize_main.get('files_read')}")
                # If geometry present, also profile bbox-only alternative for comparison
                if geometry_wkb and s2_clause:
                    where_no_s2 = [w for w in where if w != s2_clause]
                    sql_bbox_only = (
                        "SELECT scene_id, collection, datetime, bbox, cog_key, cog_href, "
                        "cog_href AS request_url, "
                        "cog_width, cog_height, cog_tile_width, cog_tile_height, cog_crs_code, cog_transform, "
                        "cog_tile_offsets, cog_tile_byte_counts, cog_dtype_code, cog_bits_per_sample, cog_compression_code, cog_predictor, "
                        "cog_dn_scale, cog_dn_offset, cog_roles "
                        f"FROM delta_scan('{table_path}') "
                        f"WHERE {' AND '.join(where_no_s2)} "
                        f"ORDER BY datetime DESC "
                        f"LIMIT {int(max(0, limit))} OFFSET {int(max(0, offset))}"
                    )
                    prof_bbox = service.profile_query(sql_bbox_only)
                    if prof_bbox:
                        summ_bbox = _summarize_explain_json(prof_bbox)
                        logger.info(f"[PROFILE bbox-only] time_ms={summ_bbox.get('latency_ms')} rows={summ_bbox.get('rows_read')} bytes={summ_bbox.get('bytes_read')} files={summ_bbox.get('files_read')}")
            except Exception as e:
                logger.debug(f"EXPLAIN ANALYZE failed: {e}")

        metrics = QueryMetrics(
            execution_time_ms=result.execution_time_ms,
            rows_returned=len(table),
            bytes_scanned=result.bytes_scanned,
            snapshot_version=result.snapshot_version,
        )

        if fmt == ResponseFormat.ARROW:
            response = create_arrow_streaming_response(table, metrics)
            # Add EXPLAIN metrics to headers if available
            if explain_metrics:
                for key, value in explain_metrics.items():
                    if value is not None:
                        response.headers[f"X-Explain-{key.replace('_', '-').title()}"] = str(value)
            return response
        else:
            data = table.to_pylist()
            columns = table.column_names
            return QueryResponse(data=data, metrics=metrics, columns=columns)
    except KeyError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"/v1/scenes:assets failed: {e}")
        raise HTTPException(status_code=500, detail="Internal error")


# Keep existing admin/query endpoints below for internal ops

@app.post("/q", include_in_schema=False)
async def execute_query(
    request: QueryRequest,
    fmt: ResponseFormat = Query(ResponseFormat.ARROW, description="Response format"),
    service: DuckDBService = Depends(get_duckdb_service)
):
    """
    Execute a SQL query against attached Delta Lake tables.

    Supports both Apache Arrow streaming (default) and JSON responses.
    """
    try:
        # Validate query safety
        QueryTemplate.validate_query_safety(request.sql)

        # Substitute parameters if provided
        final_sql = QueryTemplate.substitute_parameters(request.sql, request.parameters)

        # Execute query
        result = service.execute_query(final_sql, timeout=request.timeout)

        # Create metrics
        metrics = QueryMetrics(
            execution_time_ms=result.execution_time_ms,
            rows_returned=len(result.arrow_table),
            bytes_scanned=result.bytes_scanned,
            snapshot_version=result.snapshot_version
        )

        if fmt == ResponseFormat.ARROW:
            # Return Arrow streaming response
            return create_arrow_streaming_response(result.arrow_table, metrics)
        else:
            # Return JSON response
            # Convert Arrow table to Python objects
            data = result.arrow_table.to_pylist()
            columns = result.arrow_table.column_names

            return QueryResponse(
                data=data,
                metrics=metrics,
                columns=columns
            )

    except ValueError as e:
        # Query validation error
        raise HTTPException(
            status_code=400,
            detail=ErrorResponse(
                error=str(e),
                error_type="ValidationError",
                query=request.sql
            ).model_dump()
        )
    except TimeoutError as e:
        # Query timeout or too many concurrent queries
        if "connection pool is full" in str(e) or "Too many concurrent queries" in str(e):
            raise HTTPException(
                status_code=429,
                detail=ErrorResponse(
                    error="Too many concurrent queries. Please try again later.",
                    error_type="ConcurrencyLimitError",
                    query=request.sql
                ).model_dump()
            )
        else:
            raise HTTPException(
                status_code=408,
                detail=ErrorResponse(
                    error=str(e),
                    error_type="TimeoutError",
                    query=request.sql
                ).model_dump()
            )
    except Exception as e:
        # Other errors
        logger.error(f"Query execution failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error=str(e),
                error_type="ExecutionError",
                query=request.sql
            ).model_dump()
        )


@app.post("/q/named/{query_name}", include_in_schema=False)
async def execute_named_query(
    query_name: str,
    parameters: Dict[str, Any],
    fmt: ResponseFormat = Query(ResponseFormat.ARROW, description="Response format"),
    service: DuckDBService = Depends(get_duckdb_service)
):
    """Execute a predefined named query with parameters."""
    try:
        if query_name not in PREDEFINED_QUERIES:
            raise HTTPException(
                status_code=404,
                detail=f"Named query '{query_name}' not found"
            )

        named_query = PREDEFINED_QUERIES[query_name]

        # Create query request
        request = QueryRequest(
            sql=named_query.sql,
            parameters=parameters
        )

        # Execute using the main query endpoint logic
        return await execute_query(request, fmt, service)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Named query execution failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/q/named", include_in_schema=False)
async def list_named_queries():
    """List all available named queries."""
    return {
        "queries": [
            {
                "name": query.name,
                "description": query.description,
                "parameters": query.parameters
            }
            for query in PREDEFINED_QUERIES.values()
        ]
    }


@app.post("/tables/attach", include_in_schema=False)
async def attach_table(
    alias: str,
    path: str,
    pin_snapshot: bool = True,
    service: DuckDBService = Depends(get_duckdb_service)
):
    """Attach a Delta Lake table."""
    try:
        table_config = TableConfig(
            name=alias,
            path=path,
            alias=alias,
            pin_snapshot=pin_snapshot
        )
        service.attach_table(table_config)
        return {"message": f"Table '{alias}' attached successfully"}

    except Exception as e:
        logger.error(f"Failed to attach table: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/tables/{alias}", include_in_schema=False)
async def detach_table(
    alias: str,
    service: DuckDBService = Depends(get_duckdb_service)
):
    """Detach a Delta Lake table."""
    try:
        service.detach_table(alias)
        return {"message": f"Table '{alias}' detached successfully"}

    except Exception as e:
        logger.error(f"Failed to detach table: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/tables/{alias}/refresh", include_in_schema=False)
async def refresh_table(
    alias: str,
    service: DuckDBService = Depends(get_duckdb_service)
):
    """Refresh a table's snapshot."""
    try:
        service.refresh_table_snapshot(alias)
        return {"message": f"Table '{alias}' snapshot refreshed successfully"}

    except Exception as e:
        logger.error(f"Failed to refresh table: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/tables/{alias}", response_model=TableInfo, include_in_schema=False)
async def get_table_info(
    alias: str,
    service: DuckDBService = Depends(get_duckdb_service)
):
    """Get information about an attached table."""
    try:
        info = service.get_table_info(alias)
        return TableInfo(**info)

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to get table info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/tables", include_in_schema=False)
async def list_tables(service: DuckDBService = Depends(get_duckdb_service)):
    """List all attached tables."""
    return {
        "tables": [
            {
                "alias": alias,
                "path": config.path,
                "pin_snapshot": config.pin_snapshot
            }
            for alias, config in service.attached_tables.items()
        ]
    }
