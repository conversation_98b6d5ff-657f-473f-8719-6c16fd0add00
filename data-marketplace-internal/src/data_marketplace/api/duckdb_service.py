# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
DuckDB Service for Delta Lake querying with connection management and caching.

This module provides a production-ready DuckDB service that:
- Manages connection pools for concurrent queries
- Loads and configures delta, httpfs, and cache_httpfs extensions
- Handles S3 authentication and table attachment with PIN_SNAPSHOT
- Provides query execution with safety controls and monitoring
"""

import asyncio
import logging
import threading
import time
from contextlib import contextmanager
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Any, Generator, Tuple
from queue import Queue, Empty
import os
import psutil

import duckdb
import pyarrow as pa
from data_marketplace.config.settings import Settings

logger = logging.getLogger(__name__)


@dataclass
class TableConfig:
    """Configuration for a Delta Lake table attachment."""
    name: str
    path: str
    alias: str
    pin_snapshot: bool = True


@dataclass
class QueryResult:
    """Result of a DuckDB query execution."""
    arrow_table: pa.Table
    execution_time_ms: float
    rows_scanned: int
    bytes_scanned: int
    snapshot_version: Optional[int] = None


@dataclass
class SafetyLimits:
    """Safety limits for query execution."""
    max_result_rows: int = 1_000_000
    max_result_bytes: int = 100 * 1024 * 1024  # 100MB
    max_query_time_seconds: float = 300.0
    max_concurrent_queries: int = 8
    queue_timeout_seconds: float = 30.0


class DuckDBConnectionPool:
    """Thread-safe connection pool for DuckDB instances with concurrency control."""

    def __init__(self, pool_size: int = 4, settings: Optional[Settings] = None, safety_limits: Optional[SafetyLimits] = None):
        self.pool_size = pool_size
        self.settings = settings or Settings()
        self.safety_limits = safety_limits or SafetyLimits()
        self._pool: Queue = Queue(maxsize=pool_size)
        self._lock = threading.Lock()
        self._initialized = False

        # Use a shared on-disk database to share catalog (views) across connections
        # This avoids per-connection view visibility issues that happen with in-memory DBs
        self._db_path = os.getenv("DUCKDB_DATABASE_PATH", "/tmp/terrafloww_duckdb.db")

        # Concurrency tracking
        self._active_queries = 0
        self._query_queue: Queue = Queue(maxsize=self.safety_limits.max_concurrent_queries * 2)
        self._query_lock = threading.Lock()

    def _create_connection(self) -> duckdb.DuckDBPyConnection:
        """Create a new DuckDB connection with extensions and configuration."""
        # Open a file-backed DuckDB database so that views exist for all pool connections
        conn = duckdb.connect(self._db_path)

        # Install and load required extensions
        logger.info("Installing DuckDB extensions...")
        conn.execute("INSTALL delta")
        conn.execute("LOAD delta")
        conn.execute("INSTALL httpfs")
        conn.execute("LOAD httpfs")

        # Install cache_httpfs from community extensions
        try:
            conn.execute("INSTALL cache_httpfs FROM community")
            conn.execute("LOAD cache_httpfs")
            logger.info("cache_httpfs extension loaded successfully")
        except Exception as e:
            logger.warning(f"Failed to load cache_httpfs extension: {e}")
            logger.info("Continuing without cache_httpfs")

        # Configure memory and performance settings
        memory_limit = os.getenv("DUCKDB_MEMORY_LIMIT", "4GB")
        threads = int(os.getenv("DUCKDB_THREADS", "2"))

        conn.execute(f"SET memory_limit='{memory_limit}'")
        conn.execute(f"SET threads={threads}")
        conn.execute("SET preserve_insertion_order=false")

        # Enable object cache for better performance
        conn.execute("PRAGMA enable_object_cache")

        # Configure S3 credentials
        self._configure_s3_credentials(conn)

        # Configure cache_httpfs if available
        self._configure_cache_httpfs(conn)

        logger.info(f"DuckDB connection created with memory_limit={memory_limit}, threads={threads}, db_path={self._db_path}")
        return conn
    
    def _configure_s3_credentials(self, conn: duckdb.DuckDBPyConnection) -> None:
        """Configure S3 credentials using DuckDB secrets."""
        try:
            storage_options = self.settings.get_s3_storage_options()
            region = storage_options.get("AWS_REGION", "us-west-2")

            # Ensure region is applied regardless of credential method
            try:
                conn.execute("DROP SECRET IF EXISTS s3_secret")
            except Exception:
                pass

            if "AWS_ACCESS_KEY_ID" in storage_options and "AWS_SECRET_ACCESS_KEY" in storage_options:
                # Use explicit credentials + region
                conn.execute(f"""
                    CREATE SECRET s3_secret (
                        TYPE S3,
                        KEY_ID '{storage_options["AWS_ACCESS_KEY_ID"]}',
                        SECRET '{storage_options["AWS_SECRET_ACCESS_KEY"]}',
                        REGION '{region}'
                    )
                """)
                logger.info("S3 credentials configured with explicit keys")
            else:
                # Use credential chain (IAM roles, etc.) + region hint
                conn.execute(f"""
                    CREATE SECRET s3_secret (
                        TYPE S3,
                        PROVIDER credential_chain,
                        REGION '{region}'
                    )
                """)
                logger.info("S3 credentials configured with credential chain (region set)")

            # Also set session-level region to be safe
            try:
                conn.execute(f"SET s3_region='{region}'")
            except Exception:
                pass

        except Exception as e:
            logger.error(f"Failed to configure S3 credentials: {e}")
            raise
    
    def _configure_cache_httpfs(self, conn: duckdb.DuckDBPyConnection) -> None:
        """Configure cache_httpfs extension if available."""
        try:
            # Check if cache_httpfs is loaded
            result = conn.execute("SELECT * FROM duckdb_extensions() WHERE extension_name = 'cache_httpfs'").fetchall()
            if not result:
                return
                
            # Configure on-disk cache
            cache_dir = os.getenv("DUCKDB_CACHE_DIR", "/tmp/duckdb_cache")
            cache_block_size = int(os.getenv("DUCKDB_CACHE_BLOCK_SIZE", str(1024 * 1024)))  # 1MB in bytes

            conn.execute(f"SET cache_httpfs_type='on_disk'")
            conn.execute(f"SET cache_httpfs_cache_directory='{cache_dir}'")
            conn.execute(f"SET cache_httpfs_cache_block_size={cache_block_size}")
            
            # Enable all cache types
            conn.execute("SET cache_httpfs_enable_metadata_cache=true")
            conn.execute("SET cache_httpfs_enable_file_handle_cache=true")
            conn.execute("SET cache_httpfs_enable_glob_cache=true")
            
            logger.info(f"cache_httpfs configured with cache_dir={cache_dir}")
            
        except Exception as e:
            logger.warning(f"Failed to configure cache_httpfs: {e}")
    
    def initialize(self) -> None:
        """Initialize the connection pool."""
        with self._lock:
            if self._initialized:
                return
                
            logger.info(f"Initializing DuckDB connection pool with {self.pool_size} connections")
            for _ in range(self.pool_size):
                conn = self._create_connection()
                self._pool.put(conn)
            
            self._initialized = True
            logger.info("DuckDB connection pool initialized successfully")
    
    def _check_concurrency_limits(self) -> None:
        """Check if we can accept a new query based on concurrency limits."""
        with self._query_lock:
            if self._active_queries >= self.safety_limits.max_concurrent_queries:
                raise RuntimeError("Too many concurrent queries")

    def _increment_active_queries(self) -> None:
        """Increment the active query counter."""
        with self._query_lock:
            self._active_queries += 1

    def _decrement_active_queries(self) -> None:
        """Decrement the active query counter."""
        with self._query_lock:
            self._active_queries = max(0, self._active_queries - 1)

    @contextmanager
    def get_connection(self, timeout: float = 30.0) -> Generator[duckdb.DuckDBPyConnection, None, None]:
        """Get a connection from the pool with timeout and concurrency control."""
        if not self._initialized:
            self.initialize()

        # Check concurrency limits
        self._check_concurrency_limits()

        try:
            self._increment_active_queries()
            conn = self._pool.get(timeout=timeout)
            yield conn
        except Empty:
            self._decrement_active_queries()
            raise TimeoutError(f"Failed to get DuckDB connection within {timeout} seconds")
        except RuntimeError as e:
            # Concurrency limit exceeded
            raise TimeoutError(str(e))
        finally:
            if 'conn' in locals():
                self._pool.put(conn)
                self._decrement_active_queries()
    
    def close(self) -> None:
        """Close all connections in the pool."""
        with self._lock:
            while not self._pool.empty():
                try:
                    conn = self._pool.get_nowait()
                    conn.close()
                except Empty:
                    break
            self._initialized = False
            logger.info("DuckDB connection pool closed")


class DuckDBService:
    """High-level service for Delta Lake querying with DuckDB with safety controls."""

    def __init__(self, settings: Optional[Settings] = None, pool_size: int = 4, safety_limits: Optional[SafetyLimits] = None):
        self.settings = settings or Settings()
        self.safety_limits = safety_limits or SafetyLimits()
        self.pool = DuckDBConnectionPool(pool_size=pool_size, settings=self.settings, safety_limits=self.safety_limits)
        self.attached_tables: Dict[str, TableConfig] = {}
        self._lock = threading.Lock()
        
    def initialize(self) -> None:
        """Initialize the service and connection pool."""
        self.pool.initialize()
        logger.info("DuckDBService initialized successfully")
    
    def attach_table(self, table_config: TableConfig) -> None:
        """Attach a Delta Lake table with PIN_SNAPSHOT."""
        with self._lock:
            if table_config.alias in self.attached_tables:
                logger.warning(f"Table {table_config.alias} already attached, skipping")
                return
                
            with self.pool.get_connection() as conn:
                try:
                    # Prefer a view over ATTACH for simple table-like access
                    # Use delta_scan to expose rows as a logical table
                    create_view_sql = f"""
                        CREATE OR REPLACE VIEW {table_config.alias} AS
                        SELECT * FROM delta_scan('{table_config.path}')
                    """
                    conn.execute(create_view_sql)
                    self.attached_tables[table_config.alias] = table_config
                    logger.info(f"Attached Delta table: {table_config.alias} -> {table_config.path}")

                except Exception as e:
                    logger.error(f"Failed to attach table {table_config.alias}: {e}")
                    raise
    
    def detach_table(self, alias: str) -> None:
        """Detach a Delta Lake table."""
        with self._lock:
            if alias not in self.attached_tables:
                logger.warning(f"Table {alias} not attached, skipping detach")
                return
                
            with self.pool.get_connection() as conn:
                try:
                    # Drop view if exists; if previous ATTACH was used, best-effort DETACH
                    conn.execute(f"DROP VIEW IF EXISTS {alias}")
                    try:
                        conn.execute(f"DETACH {alias}")
                    except Exception:
                        pass
                    del self.attached_tables[alias]
                    logger.info(f"Detached Delta table: {alias}")

                except Exception as e:
                    logger.error(f"Failed to detach table {alias}: {e}")
                    raise
    
    def refresh_table_snapshot(self, alias: str) -> None:
        """Refresh a table's snapshot by detaching and reattaching."""
        with self._lock:
            if alias not in self.attached_tables:
                raise ValueError(f"Table {alias} not attached")
                
            table_config = self.attached_tables[alias]
            self.detach_table(alias)
            self.attach_table(table_config)
            logger.info(f"Refreshed snapshot for table: {alias}")
    
    def _validate_query_safety(self, sql: str) -> None:
        """Validate query safety before execution."""
        sql_upper = sql.upper().strip()

        # Only allow SELECT statements
        if not sql_upper.startswith('SELECT'):
            raise ValueError("Only SELECT statements are allowed")

        # Disallow dangerous keywords
        dangerous_keywords = [
            'DROP', 'DELETE', 'INSERT', 'UPDATE', 'CREATE', 'ALTER',
            'TRUNCATE', 'REPLACE', 'MERGE', 'COPY', 'LOAD', 'INSTALL',
            'PRAGMA', 'SET', 'RESET'
        ]

        for keyword in dangerous_keywords:
            if f' {keyword} ' in f' {sql_upper} ' or sql_upper.startswith(f'{keyword} '):
                raise ValueError(f"Keyword '{keyword}' is not allowed")

        # Basic length check
        if len(sql) > 10000:
            raise ValueError("Query is too long (max 10000 characters)")

    def _validate_result_size(self, result: pa.Table) -> None:
        """Validate that result size is within limits."""
        if len(result) > self.safety_limits.max_result_rows:
            raise ValueError(f"Result too large: {len(result)} rows (max {self.safety_limits.max_result_rows})")

        if result.nbytes > self.safety_limits.max_result_bytes:
            raise ValueError(f"Result too large: {result.nbytes} bytes (max {self.safety_limits.max_result_bytes})")

    def execute_query(self, sql: str, timeout: float = 300.0) -> QueryResult:
        """Execute a SQL query with safety controls and return results as Arrow table."""
        # Validate timeout
        if timeout > self.safety_limits.max_query_time_seconds:
            timeout = self.safety_limits.max_query_time_seconds

        # Validate query safety
        self._validate_query_safety(sql)

        start_time = time.time()

        try:
            with self.pool.get_connection(timeout=self.safety_limits.queue_timeout_seconds) as conn:
                # DuckDB 1.3 uses 'statement_timeout' (ms); older builds may differ.
                try:
                    conn.execute(f"SET statement_timeout={int(timeout * 1000)}")
                except Exception:
                    # Fallback: attempt http_timeout for S3/http operations only
                    try:
                        conn.execute(f"SET http_timeout={int(timeout * 1000)}")
                    except Exception:
                        pass

                # Execute query and get Arrow table
                result = conn.execute(sql).arrow()

                # Validate result size
                self._validate_result_size(result)

                execution_time_ms = (time.time() - start_time) * 1000

                # Get basic metrics
                rows_scanned = len(result) if result else 0
                bytes_scanned = result.nbytes if result else 0

                return QueryResult(
                    arrow_table=result,
                    execution_time_ms=execution_time_ms,
                    rows_scanned=rows_scanned,
                    bytes_scanned=bytes_scanned
                )

        except TimeoutError:
            # Re-raise timeout errors with more context
            raise TimeoutError(f"Query timed out after {timeout} seconds or connection pool is full")
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            logger.error(f"SQL: {sql}")
            raise
    
    def get_table_info(self, alias: str) -> Dict[str, Any]:
        """Get information about an attached table."""
        if alias not in self.attached_tables:
            raise ValueError(f"Table {alias} not attached")
            
        with self.pool.get_connection() as conn:
            try:
                # Get table schema
                schema_result = conn.execute(f"DESCRIBE {alias}").fetchall()
                schema = [{"column_name": row[0], "column_type": row[1]} for row in schema_result]
                
                # Get row count (approximate)
                count_result = conn.execute(f"SELECT COUNT(*) FROM {alias}").fetchone()
                row_count = count_result[0] if count_result else 0
                
                return {
                    "alias": alias,
                    "path": self.attached_tables[alias].path,
                    "schema": schema,
                    "row_count": row_count,
                    "pin_snapshot": self.attached_tables[alias].pin_snapshot
                }
                
            except Exception as e:
                logger.error(f"Failed to get table info for {alias}: {e}")
                raise
    
    def close(self) -> None:
        """Close the service and all connections."""
        self.pool.close()
        logger.info("DuckDBService closed")

    def profile_query(self, select_sql: str, timeout: float = 60.0) -> Optional[Dict[str, Any]]:
        """Run EXPLAIN ANALYZE (FORMAT json) on a SELECT and return parsed JSON.
        Returns None if profiling fails (keeps endpoints fast by default).
        """
        sql_upper = select_sql.upper().strip()
        if not sql_upper.startswith("SELECT"):
            raise ValueError("Only SELECT statements can be profiled")
        try:
            with self.pool.get_connection(timeout=self.safety_limits.queue_timeout_seconds) as conn:
                try:
                    conn.execute(f"SET statement_timeout={int(timeout * 1000)}")
                except Exception:
                    try:
                        conn.execute(f"SET http_timeout={int(timeout * 1000)}")
                    except Exception:
                        pass
                q = f"EXPLAIN ANALYZE (FORMAT json) {select_sql}"
                row = conn.execute(q).fetchone()
                if not row:
                    return None
                payload = row[0]
                import json as _json
                if isinstance(payload, (bytes, bytearray)):
                    payload = payload.decode("utf-8")
                if isinstance(payload, str):
                    return _json.loads(payload)
                return payload if isinstance(payload, dict) else None
        except Exception as e:
            logger.debug(f"profile_query failed: {e}")
            return None
