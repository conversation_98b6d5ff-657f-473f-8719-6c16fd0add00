# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Delta Lake snapshot management for automatic table refresh.

This module provides background services for:
- Monitoring Delta Lake table versions
- Automatic snapshot refresh when new versions are detected
- Configurable refresh intervals and strategies
"""

import asyncio
import logging
import threading
import time
from dataclasses import dataclass
from typing import Dict, Optional, Set, Callable
from pathlib import Path

import boto3
from botocore.exceptions import ClientError

from data_marketplace.api.duckdb_service import DuckDBService, TableConfig

logger = logging.getLogger(__name__)


@dataclass
class SnapshotInfo:
    """Information about a Delta Lake table snapshot."""
    table_alias: str
    table_path: str
    current_version: Optional[int]
    last_checked: float
    last_refreshed: float
    check_interval_seconds: float = 60.0


class DeltaSnapshotMonitor:
    """Monitor Delta Lake table snapshots and trigger refreshes."""
    
    def __init__(self, duckdb_service: DuckDBService, check_interval: float = 60.0):
        self.duckdb_service = duckdb_service
        self.check_interval = check_interval
        self.monitored_tables: Dict[str, SnapshotInfo] = {}
        self._running = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._lock = threading.Lock()
        
        # S3 client for checking Delta log files
        self._s3_client = None
        
    def _get_s3_client(self):
        """Get or create S3 client."""
        if self._s3_client is None:
            # Use the same credentials as DuckDB service
            storage_options = self.duckdb_service.settings.get_s3_storage_options()
            
            session = boto3.Session(
                aws_access_key_id=storage_options.get("AWS_ACCESS_KEY_ID"),
                aws_secret_access_key=storage_options.get("AWS_SECRET_ACCESS_KEY"),
                region_name=storage_options.get("AWS_REGION", "us-west-2")
            )
            self._s3_client = session.client('s3')
        
        return self._s3_client
    
    def _parse_s3_path(self, s3_path: str) -> tuple[str, str]:
        """Parse S3 path into bucket and key."""
        if not s3_path.startswith('s3://'):
            raise ValueError(f"Invalid S3 path: {s3_path}")
        
        path_parts = s3_path[5:].split('/', 1)
        bucket = path_parts[0]
        key = path_parts[1] if len(path_parts) > 1 else ""
        
        return bucket, key
    
    def _get_latest_delta_version(self, table_path: str) -> Optional[int]:
        """Get the latest version of a Delta Lake table."""
        try:
            if table_path.startswith('s3://'):
                return self._get_s3_delta_version(table_path)
            else:
                return self._get_local_delta_version(table_path)
        except Exception as e:
            logger.error(f"Failed to get Delta version for {table_path}: {e}")
            return None
    
    def _get_s3_delta_version(self, s3_path: str) -> Optional[int]:
        """Get the latest version from S3 Delta table."""
        bucket, key = self._parse_s3_path(s3_path)
        delta_log_prefix = f"{key}/_delta_log/" if key else "_delta_log/"
        
        s3_client = self._get_s3_client()
        
        try:
            # List objects in the _delta_log directory
            response = s3_client.list_objects_v2(
                Bucket=bucket,
                Prefix=delta_log_prefix,
                Delimiter='/'
            )
            
            if 'Contents' not in response:
                return None
            
            # Find the highest numbered .json file
            max_version = -1
            for obj in response['Contents']:
                key_name = obj['Key']
                if key_name.endswith('.json'):
                    # Extract version number from filename like 000000000000000001.json
                    filename = Path(key_name).name
                    if filename.replace('.json', '').isdigit():
                        version = int(filename.replace('.json', ''))
                        max_version = max(max_version, version)
            
            return max_version if max_version >= 0 else None
            
        except ClientError as e:
            logger.error(f"S3 error checking Delta version: {e}")
            return None
    
    def _get_local_delta_version(self, local_path: str) -> Optional[int]:
        """Get the latest version from local Delta table."""
        delta_log_dir = Path(local_path) / "_delta_log"
        
        if not delta_log_dir.exists():
            return None
        
        try:
            # Find the highest numbered .json file
            max_version = -1
            for json_file in delta_log_dir.glob("*.json"):
                if json_file.stem.isdigit():
                    version = int(json_file.stem)
                    max_version = max(max_version, version)
            
            return max_version if max_version >= 0 else None
            
        except Exception as e:
            logger.error(f"Error checking local Delta version: {e}")
            return None
    
    def add_table(self, table_alias: str, table_path: str, check_interval: float = None) -> None:
        """Add a table to be monitored for snapshot changes."""
        with self._lock:
            if check_interval is None:
                check_interval = self.check_interval
            
            current_version = self._get_latest_delta_version(table_path)
            current_time = time.time()
            
            snapshot_info = SnapshotInfo(
                table_alias=table_alias,
                table_path=table_path,
                current_version=current_version,
                last_checked=current_time,
                last_refreshed=current_time,
                check_interval_seconds=check_interval
            )
            
            self.monitored_tables[table_alias] = snapshot_info
            logger.info(f"Added table {table_alias} to snapshot monitoring (version: {current_version})")
    
    def remove_table(self, table_alias: str) -> None:
        """Remove a table from monitoring."""
        with self._lock:
            if table_alias in self.monitored_tables:
                del self.monitored_tables[table_alias]
                logger.info(f"Removed table {table_alias} from snapshot monitoring")
    
    def check_table_updates(self, table_alias: str) -> bool:
        """Check if a specific table has updates and refresh if needed."""
        with self._lock:
            if table_alias not in self.monitored_tables:
                return False
            
            snapshot_info = self.monitored_tables[table_alias]
        
        current_time = time.time()
        
        # Check if it's time to check for updates
        if current_time - snapshot_info.last_checked < snapshot_info.check_interval_seconds:
            return False
        
        # Get the latest version
        latest_version = self._get_latest_delta_version(snapshot_info.table_path)
        
        # Update last checked time
        with self._lock:
            snapshot_info.last_checked = current_time
        
        if latest_version is None:
            logger.warning(f"Could not determine version for table {table_alias}")
            return False
        
        # Check if version has changed
        if latest_version != snapshot_info.current_version:
            logger.info(f"Table {table_alias} version changed: {snapshot_info.current_version} -> {latest_version}")
            
            try:
                # Refresh the table snapshot
                self.duckdb_service.refresh_table_snapshot(table_alias)
                
                # Update snapshot info
                with self._lock:
                    snapshot_info.current_version = latest_version
                    snapshot_info.last_refreshed = current_time
                
                logger.info(f"Successfully refreshed snapshot for table {table_alias}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to refresh snapshot for table {table_alias}: {e}")
                return False
        
        return False
    
    def _monitor_loop(self) -> None:
        """Main monitoring loop that runs in a background thread."""
        logger.info("Delta snapshot monitor started")
        
        while self._running:
            try:
                # Get a copy of table aliases to avoid holding the lock
                with self._lock:
                    table_aliases = list(self.monitored_tables.keys())
                
                # Check each table for updates
                for table_alias in table_aliases:
                    if not self._running:
                        break
                    
                    try:
                        self.check_table_updates(table_alias)
                    except Exception as e:
                        logger.error(f"Error checking updates for table {table_alias}: {e}")
                
                # Sleep for a short interval before next check
                time.sleep(min(10.0, self.check_interval / 6))
                
            except Exception as e:
                logger.error(f"Error in snapshot monitor loop: {e}")
                time.sleep(10.0)
        
        logger.info("Delta snapshot monitor stopped")
    
    def start(self) -> None:
        """Start the background monitoring thread."""
        if self._running:
            logger.warning("Snapshot monitor is already running")
            return
        
        self._running = True
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        logger.info("Started Delta snapshot monitor")
    
    def stop(self) -> None:
        """Stop the background monitoring thread."""
        if not self._running:
            return
        
        self._running = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=30.0)
        
        logger.info("Stopped Delta snapshot monitor")
    
    def get_status(self) -> Dict[str, dict]:
        """Get the current status of all monitored tables."""
        with self._lock:
            return {
                alias: {
                    "table_path": info.table_path,
                    "current_version": info.current_version,
                    "last_checked": info.last_checked,
                    "last_refreshed": info.last_refreshed,
                    "check_interval_seconds": info.check_interval_seconds
                }
                for alias, info in self.monitored_tables.items()
            }
