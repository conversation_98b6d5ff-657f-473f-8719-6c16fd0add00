# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Monitoring and observability for the Delta Lake API.

This module provides:
- Prometheus metrics collection
- Structured logging for requests
- Performance monitoring
- Health checks and diagnostics
"""

import logging
import time
from contextlib import contextmanager
from dataclasses import dataclass
from typing import Dict, Optional, Any
import json

try:
    from prometheus_client import Counter, Histogram, Gauge, Info, generate_latest, CONTENT_TYPE_LATEST
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False
    # Create dummy classes for when prometheus is not available
    class Counter:
        def __init__(self, *args, **kwargs): pass
        def inc(self, *args, **kwargs): pass
        def labels(self, *args, **kwargs): return self
    
    class Histogram:
        def __init__(self, *args, **kwargs): pass
        def observe(self, *args, **kwargs): pass
        def labels(self, *args, **kwargs): return self
        def time(self): return contextmanager(lambda: iter([None]))()
    
    class Gauge:
        def __init__(self, *args, **kwargs): pass
        def set(self, *args, **kwargs): pass
        def inc(self, *args, **kwargs): pass
        def dec(self, *args, **kwargs): pass
        def labels(self, *args, **kwargs): return self
    
    class Info:
        def __init__(self, *args, **kwargs): pass
        def info(self, *args, **kwargs): pass

logger = logging.getLogger(__name__)


@dataclass
class QueryMetrics:
    """Metrics for a single query execution."""
    query_id: str
    sql: str
    execution_time_ms: float
    rows_returned: int
    bytes_scanned: int
    bytes_returned: int
    table_aliases: list[str]
    response_format: str
    success: bool
    error_type: Optional[str] = None
    error_message: Optional[str] = None


class PrometheusMetrics:
    """Prometheus metrics collector for the Delta Lake API."""
    
    def __init__(self):
        if not PROMETHEUS_AVAILABLE:
            logger.warning("Prometheus client not available, metrics will be disabled")
            return
        
        # Query metrics
        self.queries_total = Counter(
            'duckdb_queries_total',
            'Total number of queries executed',
            ['table', 'format', 'status']
        )
        
        self.query_duration = Histogram(
            'duckdb_query_duration_seconds',
            'Query execution time in seconds',
            ['table', 'format'],
            buckets=[0.1, 0.5, 1.0, 2.5, 5.0, 10.0, 30.0, 60.0, 120.0, 300.0]
        )
        
        self.query_rows_returned = Histogram(
            'duckdb_query_rows_returned',
            'Number of rows returned by queries',
            ['table', 'format'],
            buckets=[1, 10, 100, 1000, 10000, 100000, 1000000]
        )
        
        self.query_bytes_scanned = Histogram(
            'duckdb_query_bytes_scanned',
            'Bytes scanned during query execution',
            ['table'],
            buckets=[1024, 10240, 102400, 1048576, 10485760, 104857600, 1073741824]
        )
        
        self.query_bytes_returned = Histogram(
            'duckdb_query_bytes_returned',
            'Bytes returned by queries',
            ['format'],
            buckets=[1024, 10240, 102400, 1048576, 10485760, 104857600, 1073741824]
        )
        
        # Connection pool metrics
        self.active_connections = Gauge(
            'duckdb_active_connections',
            'Number of active DuckDB connections'
        )
        
        self.queries_in_flight = Gauge(
            'duckdb_queries_in_flight',
            'Number of queries currently executing'
        )
        
        # Table metrics
        self.attached_tables = Gauge(
            'duckdb_attached_tables',
            'Number of attached Delta Lake tables'
        )
        
        self.table_snapshot_version = Gauge(
            'duckdb_table_snapshot_version',
            'Current snapshot version for each table',
            ['table', 'path']
        )
        
        self.table_last_refresh = Gauge(
            'duckdb_table_last_refresh_timestamp',
            'Timestamp of last table refresh',
            ['table']
        )
        
        # Error metrics
        self.errors_total = Counter(
            'duckdb_errors_total',
            'Total number of errors',
            ['error_type', 'endpoint']
        )
        
        # System info
        self.info = Info(
            'duckdb_api_info',
            'Information about the DuckDB API service'
        )
    
    def record_query(self, metrics: QueryMetrics) -> None:
        """Record metrics for a completed query."""
        if not PROMETHEUS_AVAILABLE:
            return
        
        # Determine primary table (first one mentioned)
        primary_table = metrics.table_aliases[0] if metrics.table_aliases else "unknown"
        
        # Record query completion
        status = "success" if metrics.success else "error"
        self.queries_total.labels(
            table=primary_table,
            format=metrics.response_format,
            status=status
        ).inc()
        
        if metrics.success:
            # Record timing
            self.query_duration.labels(
                table=primary_table,
                format=metrics.response_format
            ).observe(metrics.execution_time_ms / 1000.0)
            
            # Record data metrics
            self.query_rows_returned.labels(
                table=primary_table,
                format=metrics.response_format
            ).observe(metrics.rows_returned)
            
            self.query_bytes_scanned.labels(
                table=primary_table
            ).observe(metrics.bytes_scanned)
            
            self.query_bytes_returned.labels(
                format=metrics.response_format
            ).observe(metrics.bytes_returned)
        else:
            # Record error
            self.errors_total.labels(
                error_type=metrics.error_type or "unknown",
                endpoint="query"
            ).inc()
    
    def update_connection_metrics(self, active_connections: int, queries_in_flight: int) -> None:
        """Update connection pool metrics."""
        if not PROMETHEUS_AVAILABLE:
            return
        
        self.active_connections.set(active_connections)
        self.queries_in_flight.set(queries_in_flight)
    
    def update_table_metrics(self, attached_tables: int, table_info: Dict[str, Dict]) -> None:
        """Update table-related metrics."""
        if not PROMETHEUS_AVAILABLE:
            return
        
        self.attached_tables.set(attached_tables)
        
        for table_alias, info in table_info.items():
            if 'snapshot_version' in info and info['snapshot_version'] is not None:
                self.table_snapshot_version.labels(
                    table=table_alias,
                    path=info.get('path', 'unknown')
                ).set(info['snapshot_version'])
            
            if 'last_refresh' in info:
                self.table_last_refresh.labels(
                    table=table_alias
                ).set(info['last_refresh'])
    
    def set_service_info(self, version: str, duckdb_version: str) -> None:
        """Set service information."""
        if not PROMETHEUS_AVAILABLE:
            return
        
        self.info.info({
            'version': version,
            'duckdb_version': duckdb_version
        })


class StructuredLogger:
    """Structured logging for API requests and operations."""
    
    def __init__(self, logger_name: str = __name__):
        self.logger = logging.getLogger(logger_name)
    
    def log_query_start(self, query_id: str, sql: str, parameters: Optional[Dict] = None) -> None:
        """Log the start of a query execution."""
        self.logger.info(
            "Query started",
            extra={
                "event_type": "query_start",
                "query_id": query_id,
                "sql_length": len(sql),
                "has_parameters": parameters is not None,
                "parameter_count": len(parameters) if parameters else 0
            }
        )
    
    def log_query_complete(self, metrics: QueryMetrics) -> None:
        """Log the completion of a query execution."""
        log_level = logging.INFO if metrics.success else logging.ERROR
        
        self.logger.log(
            log_level,
            f"Query {'completed' if metrics.success else 'failed'}",
            extra={
                "event_type": "query_complete",
                "query_id": metrics.query_id,
                "success": metrics.success,
                "execution_time_ms": metrics.execution_time_ms,
                "rows_returned": metrics.rows_returned,
                "bytes_scanned": metrics.bytes_scanned,
                "bytes_returned": metrics.bytes_returned,
                "table_aliases": metrics.table_aliases,
                "response_format": metrics.response_format,
                "error_type": metrics.error_type,
                "error_message": metrics.error_message
            }
        )
    
    def log_table_operation(self, operation: str, table_alias: str, table_path: str, success: bool, error: Optional[str] = None) -> None:
        """Log table management operations."""
        log_level = logging.INFO if success else logging.ERROR
        
        self.logger.log(
            log_level,
            f"Table {operation} {'completed' if success else 'failed'}",
            extra={
                "event_type": "table_operation",
                "operation": operation,
                "table_alias": table_alias,
                "table_path": table_path,
                "success": success,
                "error": error
            }
        )
    
    def log_snapshot_refresh(self, table_alias: str, old_version: Optional[int], new_version: Optional[int], success: bool) -> None:
        """Log snapshot refresh operations."""
        log_level = logging.INFO if success else logging.WARNING
        
        self.logger.log(
            log_level,
            f"Snapshot refresh {'completed' if success else 'failed'}",
            extra={
                "event_type": "snapshot_refresh",
                "table_alias": table_alias,
                "old_version": old_version,
                "new_version": new_version,
                "success": success
            }
        )


class MonitoringService:
    """Central monitoring service that coordinates metrics and logging."""
    
    def __init__(self):
        self.prometheus = PrometheusMetrics()
        self.logger = StructuredLogger()
        self._query_counter = 0
    
    def generate_query_id(self) -> str:
        """Generate a unique query ID."""
        self._query_counter += 1
        return f"q_{int(time.time())}_{self._query_counter}"
    
    @contextmanager
    def track_query(self, sql: str, table_aliases: list[str], response_format: str, parameters: Optional[Dict] = None):
        """Context manager to track query execution."""
        query_id = self.generate_query_id()
        start_time = time.time()
        
        # Log query start
        self.logger.log_query_start(query_id, sql, parameters)
        
        try:
            yield query_id
            # Query succeeded - metrics will be recorded by the caller
        except Exception as e:
            # Query failed
            execution_time_ms = (time.time() - start_time) * 1000
            
            error_type = type(e).__name__
            error_message = str(e)
            
            metrics = QueryMetrics(
                query_id=query_id,
                sql=sql,
                execution_time_ms=execution_time_ms,
                rows_returned=0,
                bytes_scanned=0,
                bytes_returned=0,
                table_aliases=table_aliases,
                response_format=response_format,
                success=False,
                error_type=error_type,
                error_message=error_message
            )
            
            self.record_query_metrics(metrics)
            raise
    
    def record_query_metrics(self, metrics: QueryMetrics) -> None:
        """Record metrics for a completed query."""
        self.prometheus.record_query(metrics)
        self.logger.log_query_complete(metrics)
    
    def get_metrics_text(self) -> str:
        """Get Prometheus metrics in text format."""
        if not PROMETHEUS_AVAILABLE:
            return "# Prometheus client not available\n"
        
        return generate_latest().decode('utf-8')
    
    def get_metrics_content_type(self) -> str:
        """Get the content type for Prometheus metrics."""
        return CONTENT_TYPE_LATEST if PROMETHEUS_AVAILABLE else "text/plain"


# Global monitoring instance
monitoring = MonitoringService()
