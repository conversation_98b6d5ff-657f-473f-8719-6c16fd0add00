# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Lightweight dataset registry for public API.

Maps a public dataset name (e.g., "sentinel-2-l2a") to an internal
DuckDB table alias (attached with TYPE delta and optional PIN_SNAPSHOT),
and provides minimal metadata for discovery.

Keep this tiny and easy to extend without exposing internal storage paths.
"""
from __future__ import annotations
from dataclasses import dataclass
from typing import Dict, List


@dataclass(frozen=True)
class DatasetInfo:
    name: str
    description: str
    table_alias: str  # Attached DuckDB table alias
    supported_filters: Dict[str, str]


# Minimal initial registry. Update table_alias to match your attached alias.
DATASETS: Dict[str, DatasetInfo] = {
    "sentinel-2-l2a": DatasetInfo(
        name="sentinel-2-l2a",
        description="Sentinel-2 L2A surface reflectance scenes and assets",
        table_alias="default_table",  # Will be dynamically resolved
        supported_filters={
            "date_start": "YYYY-MM-DD",
            "date_end": "YYYY-MM-DD",
            "bbox": "minx,miny,maxx,maxy (WGS84)",
            "cloud_cover_max": "float (optional)",
            "assets": "comma-separated asset keys (optional)",
            "limit": "int",
            "offset": "int",
        },
    ),
}


def list_datasets() -> List[Dict[str, object]]:
    return [
        {
            "name": d.name,
            "description": d.description,
            "filters": d.supported_filters,
        }
        for d in DATASETS.values()
    ]


def get_table_alias(dataset: str) -> str:
    """
    Get table alias for a dataset.
    Returns the first attached table if available, otherwise falls back to configured alias.
    """
    info = DATASETS.get(dataset)
    if not info:
        raise KeyError(f"Unknown dataset: {dataset}")

    # Try to get the first attached table dynamically
    try:
        from data_marketplace.api.endpoints import get_duckdb_service
        service = get_duckdb_service()
        if service.attached_tables:
            # Return the first attached table alias
            first_alias = next(iter(service.attached_tables.keys()))
            return first_alias
    except Exception:
        # Fall back to configured alias if service not available
        pass

    return info.table_alias

