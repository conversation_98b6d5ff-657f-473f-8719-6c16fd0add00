# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""Delta Lake table management and operations."""

import logging
from typing import Optional, Dict, Any, List
from pathlib import Path
import pyarrow as pa
from deltalake import DeltaTable, write_deltalake
from data_marketplace.config.settings import Settings

logger = logging.getLogger(__name__)


class DeltaManager:
    """Manager for Delta Lake table operations."""

    def __init__(self, settings: Optional[Settings] = None):
        """
        Initialize Delta Lake manager.

        Args:
            settings: Settings instance (uses global if None)
        """
        if settings is None:
            from data_marketplace.config.settings import settings as global_settings

            settings = global_settings

        self.settings = settings
        self.logger = logger

        # Cache to avoid redundant table existence checks and DeltaTable instantiations
        self._table_exists_cache = {}
        self._delta_table_cache = {}

    def get_table_path(self, table_name: str) -> str:
        """Get full Delta table path."""
        return self.settings.get_delta_table_path(table_name)

    def _check_table_exists_cached(self, table_uri: str, storage_opts: dict) -> bool:
        """
        Check if Delta table exists with caching to avoid redundant S3 calls.

        Args:
            table_uri: Full table URI
            storage_opts: Storage options for S3

        Returns:
            True if table exists
        """
        # Use table_uri as cache key
        if table_uri in self._table_exists_cache:
            return self._table_exists_cache[table_uri]

        # Check table existence
        table_exists = False
        if table_uri.startswith("s3://"):
            try:
                self.logger.info(f"🔍 Checking if S3 Delta table exists at {table_uri} (first time)")
                DeltaTable(table_uri, storage_options=storage_opts)
                table_exists = True
                self.logger.info(f"✅ Delta table exists at {table_uri}")
            except Exception as e:
                table_exists = False
                self.logger.info(f"ℹ️  Delta table does not exist at {table_uri}: {e}")
        else:
            table_exists = Path(table_uri).exists()
            self.logger.info(f"🔍 Local table exists: {table_exists}")

        # Cache the result
        self._table_exists_cache[table_uri] = table_exists
        return table_exists

    def clear_cache(self):
        """Clear all caches (useful for testing or when table state changes externally)."""
        self._table_exists_cache.clear()
        self._delta_table_cache.clear()
        self.logger.debug("Cleared DeltaManager caches")

    def table_exists(self, table_name: str) -> bool:
        """Check if Delta table exists."""
        try:
            table_path = self.get_table_path(table_name)
            DeltaTable(
                table_path, storage_options=self.settings.get_s3_storage_options()
            )
            return True
        except Exception:
            return False

    def create_table(
        self,
        table_name: str,
        schema: pa.Schema,
        partition_by: Optional[List[str]] = None,
    ) -> bool:
        """
        Create a new Delta table.

        Args:
            table_name: Name of the table
            schema: PyArrow schema for the table
            partition_by: List of columns to partition by

        Returns:
            True if successful
        """
        try:
            table_path = self.get_table_path(table_name)

            # Create empty table with schema
            empty_table = pa.table([], schema=schema)

            write_deltalake(
                table_path,
                empty_table,
                mode="error",  # Fail if table exists
                partition_by=partition_by,
                storage_options=self.settings.get_s3_storage_options(),
            )

            self.logger.info(f"Created Delta table: {table_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to create table {table_name}: {e}")
            return False

    def write_table(
        self,
        table_name: str,
        data: pa.Table,
        mode: str = "append",
        partition_by: Optional[List[str]] = None,
    ) -> bool:
        """
        Write data to Delta table.

        Args:
            table_name: Name of the table
            data: PyArrow table with data
            mode: Write mode ("append", "overwrite", "error", "ignore")
            partition_by: List of columns to partition by

        Returns:
            True if successful
        """
        try:
            table_path = self.get_table_path(table_name)

            write_deltalake(
                table_path,
                data,
                mode=mode,
                partition_by=partition_by,
                storage_options=self.settings.get_s3_storage_options(),
                # Parquet settings
                parquet_row_group_size=self.settings.parquet.row_group_size,
                parquet_compression=self.settings.parquet.compression,
                parquet_compression_level=self.settings.parquet.compression_level,
            )

            self.logger.info(f"Wrote {len(data)} rows to table {table_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to write to table {table_name}: {e}")
            return False

    def read_table(self, table_name: str) -> Optional[DeltaTable]:
        """
        Read Delta table.

        Args:
            table_name: Name of the table

        Returns:
            DeltaTable instance or None if not found
        """
        try:
            table_path = self.get_table_path(table_name)
            return DeltaTable(
                table_path, storage_options=self.settings.get_s3_storage_options()
            )
        except Exception as e:
            self.logger.error(f"Failed to read table {table_name}: {e}")
            return None

    def list_tables(self) -> List[str]:
        """List all Delta tables."""
        from data_marketplace.storage.s3_config import S3Config

        s3_config = S3Config(self.settings)
        return s3_config.list_tables()

    def delete_table(self, table_name: str) -> bool:
        """Delete a Delta table."""
        from data_marketplace.storage.s3_config import S3Config

        s3_config = S3Config(self.settings)
        return s3_config.delete_table(table_name)

    def vacuum_table(
        self, table_name: str, retention_hours: Optional[int] = None
    ) -> bool:
        """
        Vacuum a Delta table to remove old files.

        Args:
            table_name: Name of the table
            retention_hours: Retention period (uses setting default if None)

        Returns:
            True if successful
        """
        try:
            table = self.read_table(table_name)
            if table is None:
                return False

            if retention_hours is None:
                retention_hours = self.settings.delta.vacuum_retention_hours

            table.vacuum(retention_hours=retention_hours)
            self.logger.info(f"Vacuumed table {table_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to vacuum table {table_name}: {e}")
            return False

    def write_with_merge_deduplication(
        self,
        table_name: str,
        data: pa.Table,
        merge_key: Any = "scene_id",  # Can be str or List[str] for composite keys
        partition_by: Optional[List[str]] = None,
        writer_properties=None,
    ) -> None:
        """
        Write data using MERGE for automatic deduplication (by logical name/path from Settings).
        """
        table_path = self.get_table_path(table_name)
        self.write_with_merge_deduplication_uri(
            table_uri=table_path,
            data=data,
            merge_key=merge_key,
            partition_by=partition_by,
            writer_properties=writer_properties,
            storage_options=self.settings.get_s3_storage_options(),
        )

    def write_with_merge_deduplication_uri(
        self,
        table_uri: str,
        data: pa.Table,
        merge_key: Any = "scene_id",  # Can be str or List[str] for composite keys
        partition_by: Optional[List[str]] = None,
        writer_properties=None,
        storage_options: Optional[Dict[str, str]] = None,
        force_append: bool = False,  # New parameter to force APPEND mode
    ) -> None:
        """
        Write data using MERGE for automatic deduplication (explicit table URI).

        Args:
            force_append: If True, use APPEND instead of MERGE (when pre-filtering ensures no duplicates)
        """
        storage_opts = storage_options or self.settings.get_s3_storage_options()

        logger.info(f"🔄 DeltaManager: Starting write to {table_uri}")
        logger.info(f"📊 Data: {len(data)} rows, {len(data.columns)} columns")
        logger.info(f"🔑 Storage options: {storage_opts}")
        if force_append:
            logger.info(
                "⚡ Using APPEND mode (pre-filtering active - no duplicates expected)"
            )

        try:
            # Check if table exists (cached to avoid redundant S3 calls)
            table_exists = self._check_table_exists_cached(table_uri, storage_opts)

            if not table_exists:
                # First write - create table with append mode
                logger.info(f"🆕 Creating new Delta table at: {table_uri}")
                logger.info(f"📝 Partition by: {partition_by or ['year', 'month']}")
                write_deltalake(
                    table_or_uri=table_uri,
                    data=data,
                    mode="append",
                    partition_by=partition_by or ["year", "month"],
                    storage_options=storage_opts,
                    writer_properties=writer_properties,
                )
                logger.info(
                    f"✅ Successfully created Delta table with {len(data)} rows"
                )
                # Update cache since table now exists
                self._table_exists_cache[table_uri] = True
            elif force_append:
                # Table exists but we're forcing APPEND (pre-filtering ensures no duplicates)
                logger.info(
                    f"⚡ Using APPEND mode (pre-filtering active) at {table_uri}"
                )
                write_deltalake(
                    table_or_uri=table_uri,
                    data=data,
                    mode="append",
                    partition_by=partition_by or ["year", "month"],
                    storage_options=storage_opts,
                    writer_properties=writer_properties,
                )
                logger.info(
                    f"✅ Successfully appended {len(data)} rows (no MERGE needed)"
                )
            else:
                # Table exists - use MERGE for deduplication
                logger.info(f"🔄 Using MERGE for deduplication at {table_uri}")

                # Build predicate for single or composite keys
                if isinstance(merge_key, list):
                    # Composite key: target.col1 = source.col1 AND target.col2 = source.col2
                    predicate_parts = [
                        f"target.{key} = source.{key}" for key in merge_key
                    ]
                    predicate = " AND ".join(predicate_parts)
                    logger.info(f"🔑 Using composite merge key: {merge_key}")
                else:
                    # Single key
                    predicate = f"target.{merge_key} = source.{merge_key}"
                    logger.info(f"🔑 Using single merge key: {merge_key}")

                # Use chunked MERGE to avoid OOM issues with large batches
                self._chunked_merge_operation(
                    table_uri, data, predicate, storage_opts
                )

        except Exception as e:
            logger.error(
                f"❌ Error in MERGE operation for {table_uri}: {e}", exc_info=True
            )
            # Fallback to simple append if MERGE fails
            logger.warning(f"⚠️  Falling back to append mode for {table_uri}")
            try:
                write_deltalake(
                    table_or_uri=table_uri,
                    data=data,
                    mode="append",
                    partition_by=partition_by or ["year", "month"],
                    storage_options=storage_opts,
                    writer_properties=writer_properties,
                )
                logger.info(f"✅ Fallback append successful with {len(data)} rows")
            except Exception as fallback_e:
                logger.error(
                    f"❌ Fallback append also failed: {fallback_e}", exc_info=True
                )
                raise fallback_e

    def _chunked_merge_operation(
        self,
        table_uri: str,
        data: pa.Table,
        predicate: str,
        storage_opts: Dict[str, str],
        chunk_size: int = 2000  # Match streaming processor output_batch_size (proven optimal for Delta Lake)
    ) -> None:
        """
        Perform MERGE operation in chunks to avoid OOM issues with large batches.

        Uses the same chunk size as the streaming processor's output_batch_size (4000 rows)
        which is proven optimal for Delta Lake writes. This ensures consistent memory usage
        between APPEND and MERGE operations.

        Args:
            table_uri: Delta table URI
            data: PyArrow table to merge
            predicate: MERGE predicate string
            storage_opts: Storage options
            chunk_size: Number of rows per chunk (default: 4000, matches APPEND batch size)
        """
        import math

        total_rows = len(data)
        total_chunks = math.ceil(total_rows / chunk_size)

        logger.info(f"📦 Chunked MERGE: {total_rows} rows in {total_chunks} chunks of {chunk_size} rows each")

        total_merged = 0

        for i in range(0, total_rows, chunk_size):
            chunk_end = min(i + chunk_size, total_rows)
            chunk = data.slice(i, chunk_end - i)
            chunk_num = (i // chunk_size) + 1

            logger.info(f"🔄 Merging chunk {chunk_num}/{total_chunks} ({len(chunk)} rows)...")

            try:
                dt = DeltaTable(table_uri, storage_options=storage_opts)
                (
                    dt.merge(
                        source=chunk,
                        predicate=predicate,
                        source_alias="source",
                        target_alias="target",
                    )
                    .when_matched_update_all()
                    .when_not_matched_insert_all()
                    .execute()
                )
                total_merged += len(chunk)
                logger.info(f"✅ Chunk {chunk_num}/{total_chunks} merged successfully")

            except Exception as e:
                logger.error(f"❌ Error merging chunk {chunk_num}/{total_chunks}: {e}")
                raise e

        logger.info(f"✅ Successfully merged {total_merged} rows in {total_chunks} chunks")

    def optimize_table(self, table_name: str) -> bool:
        """Optimize by logical name/path from Settings."""
        table_path = self.get_table_path(table_name)
        return self.optimize_table_uri(table_path)

    def optimize_table_uri(
        self, table_uri: str, storage_options: Optional[Dict[str, str]] = None
    ) -> bool:
        """Optimize Delta Lake table at explicit URI to consolidate small files."""
        storage_opts = storage_options or self.settings.get_s3_storage_options()

        try:
            # For S3, try to load the table directly
            if table_uri.startswith("s3://"):
                try:
                    dt = DeltaTable(table_uri, storage_options=storage_opts)
                    logger.info(f"🔧 Optimizing Delta table: {table_uri}")
                    dt.optimize.compact(max_concurrent_tasks=2)  # Memory-conservative concurrency
                    logger.info(f"✅ Table optimized: {table_uri}")
                    return True
                except Exception as e:
                    if (
                        "not found" in str(e).lower()
                        or "does not exist" in str(e).lower()
                    ):
                        logger.info(
                            f"ℹ️  No Delta table found yet for {table_uri} - skipping optimization"
                        )
                        return False
                    else:
                        raise e
            else:
                if Path(table_uri).exists():
                    dt = DeltaTable(table_uri, storage_options=storage_opts)
                    logger.info(f"🔧 Optimizing Delta table: {table_uri}")
                    dt.optimize.compact(max_concurrent_tasks=2)  # Memory-conservative concurrency
                    logger.info(f"✅ Table optimized: {table_uri}")
                    return True
                else:
                    logger.info(
                        f"ℹ️  No Delta table found yet for {table_uri} - skipping optimization"
                    )
                    return False
        except Exception as e:
            logger.warning(f"⚠️  Delta table optimization failed for {table_uri}: {e}")
            return False
